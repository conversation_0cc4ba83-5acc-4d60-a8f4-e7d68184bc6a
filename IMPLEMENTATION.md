# Implémentation des Services Order Interface et Cart Interface

## 📋 Vue d'ensemble

Cette implémentation fournit une solution complète pour la gestion des paniers, commandes, factures, contrats et paiements dans l'application items-app.

## 🏗️ Architecture

### Services Métier

#### 1. **Cart Interface Service** (`ICartService` / `CartService`)
Gestion complète des paniers d'achat :
- ✅ Obtenir ou créer un panier pour un utilisateur
- ✅ Ajouter une commande au panier
- ✅ Obtenir le contenu du panier d'un utilisateur
- ✅ Mettre à jour la quantité d'une commande
- ✅ Retirer une commande du panier
- ✅ Vider le panier
- ✅ Calcul automatique du montant total

#### 2. **Order Interface Service** (`IOrderService` / `OrderService`)
Gestion des commandes :
- ✅ Créer une commande à partir d'un panier
- ✅ Obtenir la liste des commandes d'un utilisateur
- ✅ Obtenir les détails d'une commande spécifique
- ✅ Mettre à jour le statut d'une commande (PENDING → CONFIRMED)
- ✅ Validation des transitions de statut

#### 3. **Invoice Service** (`IInvoiceService` / `InvoiceService`)
Gestion des factures :
- ✅ Générer une facture pour une commande
- ✅ Obtenir la facture associée à une commande
- ✅ Mettre à jour le statut d'une facture (SENT → PAID)
- ✅ Obtenir les factures en retard
- ✅ Validation des transitions de statut

#### 4. **Contract Service** (`IContractService` / `ContractService`)
Gestion des contrats :
- ✅ Générer un contrat pour une commande
- ✅ Enregistrer la signature d'un contrat
- ✅ Obtenir le contrat associé à une commande
- ✅ Génération automatique d'URL de document

#### 5. **Payment Service** (`IPaymentService` / `PaymentService`)
Gestion des paiements :
- ✅ Enregistrer un paiement pour une facture
- ✅ Obtenir la liste des paiements pour une facture
- ✅ Mettre à jour le statut d'un paiement
- ✅ Mise à jour automatique du statut de facture quand entièrement payée
- ✅ Validation des transitions de statut

#### 6. **Checkout Service** (`ICheckoutService` / `CheckoutService`)
Processus de transition du panier :
- ✅ Lit le Cart et ses Order pour un utilisateur
- ✅ Valide le contenu (disponibilité des articles, prix, etc.)
- ✅ Génère l'entité Invoice associée
- ✅ Met à jour le statut du Cart à CHECKED_OUT
- ✅ Génère automatiquement un contrat

### API REST

#### Cart API (`/api/v1/carts`)
- `GET /{accountId}` - Obtenir/créer panier
- `POST /{accountId}/orders` - Ajouter commande au panier
- `PUT /{accountId}/orders/{orderId}/quantity` - Modifier quantité
- `DELETE /{accountId}/orders/{orderId}` - Retirer commande
- `DELETE /{accountId}/clear` - Vider panier
- `POST /{accountId}/checkout` - Processus de checkout

#### Order API (`/api/v1/orders`)
- `GET /account/{accountId}` - Commandes d'un utilisateur
- `GET /{orderId}` - Détails d'une commande
- `PUT /{orderId}/status` - Mettre à jour statut commande
- `GET /{orderId}/invoice` - Facture d'une commande
- `GET /{orderId}/invoice/payments` - Paiements d'une facture
- `POST /{orderId}/invoice/payments` - Enregistrer paiement
- `GET /{orderId}/contract` - Contrat d'une commande
- `PUT /{orderId}/contract/sign` - Signer contrat

## 🧪 Tests

### Tests Unitaires
- **CartServiceTest** - Tests complets du service panier
- **CheckoutServiceTest** - Tests du processus de checkout
- **InvoiceServiceTest** - Tests du service facture
- **PaymentServiceTest** - Tests du service paiement

### Tests d'Intégration
- **CartResourceTest** - Tests des endpoints REST du panier
- **OrderResourceTest** - Tests des endpoints REST des commandes

### Exécution des Tests
```bash
# Tous les tests
./run-tests.sh

# Tests spécifiques
./mvnw test -Dtest=CartServiceTest
./mvnw test -Dtest=CheckoutServiceTest

# Tests avec couverture
./mvnw clean test jacoco:report
```

## 📊 Validation des Statuts

### Transitions de Statut Order
- `IN_CART` → `PENDING` | `CANCELLED`
- `PENDING` → `CONFIRMED` | `CANCELLED`
- `CONFIRMED` → `CANCELLED`
- `CANCELLED` → ❌ (final)

### Transitions de Statut Invoice
- `DRAFT` → `SENT`
- `SENT` → `PAID` | `OVERDUE`
- `OVERDUE` → `PAID`
- `PAID` → ❌ (final)

### Transitions de Statut Payment
- `PENDING` → `SUCCESSFUL` | `FAILED`
- `SUCCESSFUL` → ❌ (final)
- `FAILED` → `PENDING`

### Transitions de Statut Cart
- `ACTIVE` → `CHECKED_OUT`
- `CHECKED_OUT` → ❌ (final)

## 🔧 Fonctionnalités Avancées

### Calcul Automatique des Montants
- **Panier** : Somme de tous les ordres
- **Purchase** : `quantity × unitPrice`
- **Rental** : `quantity × unitPrice × durée_en_jours`

### Validation Métier
- Vérification de la disponibilité des articles
- Validation des quantités et prix
- Contrôle des transitions de statut
- Vérification de l'unicité des transaction IDs

### Gestion d'Erreurs
- `FunctionalErrorException` pour les erreurs métier
- Messages d'erreur explicites
- Validation des paramètres d'entrée

## 🚀 Utilisation

### Exemple : Processus Complet

```java
// 1. Créer/obtenir un panier
Cart cart = cartService.getOrCreateActiveCart("user-123");

// 2. Ajouter des articles au panier
OrderRequest orderRequest = OrderRequest.builder()
    .articleId("car-456")
    .quantity(1)
    .unitPrice(BigDecimal.valueOf(25000))
    .orderType("PURCHASE")
    .build();

Order order = orderFactory.createOrderFromRequest(orderRequest, article);
cart = cartService.addOrderToCart("user-123", order);

// 3. Processus de checkout
Invoice invoice = checkoutService.processCheckout("user-123");

// 4. Enregistrer un paiement
Payment payment = paymentService.recordPayment(
    invoice.getInvoiceId(),
    BigDecimal.valueOf(25000),
    PaymentMethod.CREDIT_CARD,
    "txn-789"
);

// 5. Signer le contrat
Contract contract = contractService.getContractByOrderId(order.getOrderId());
contractService.signContract(contract.getContractId(), LocalDateTime.now());
```

### Exemple : API REST

```bash
# Ajouter un article au panier
curl -X POST http://localhost:8080/api/v1/carts/user-123/orders \
  -H "Content-Type: application/json" \
  -d '{
    "articleId": "car-456",
    "quantity": 1,
    "unitPrice": 25000,
    "orderType": "PURCHASE"
  }'

# Processus de checkout
curl -X POST http://localhost:8080/api/v1/carts/user-123/checkout

# Enregistrer un paiement
curl -X POST http://localhost:8080/api/v1/orders/order-123/invoice/payments \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 25000,
    "paymentMethod": "CREDIT_CARD",
    "transactionId": "txn-789"
  }'
```

## 📁 Structure des Fichiers

```
src/main/java/sarl/eazycar/items/
├── application/
│   ├── dto/                     # DTOs
│   │   ├── CartDto.java
│   │   ├── OrderDto.java
│   │   ├── InvoiceDto.java
│   │   ├── ContractDto.java
│   │   └── PaymentDto.java
│   └── rest/                    # API REST
│       ├── api/
│       │   ├── CartApi.java
│       │   └── OrderApi.java
│       ├── request/
│       │   ├── OrderRequest.java
│       │   └── PaymentRequest.java
│       ├── CartResource.java
│       └── OrderResource.java
├── domain/service/              # Services métier
│   ├── ICartService.java
│   ├── IOrderService.java
│   ├── IInvoiceService.java
│   ├── IContractService.java
│   ├── IPaymentService.java
│   ├── ICheckoutService.java
│   └── impl/
│       ├── CartService.java
│       ├── OrderService.java
│       ├── InvoiceService.java
│       ├── ContractService.java
│       ├── PaymentService.java
│       └── CheckoutService.java
└── infrastructure/
    ├── factory/
    │   └── OrderFactory.java   # Factory pour créer des ordres
    └── mapper/                 # Mappers MapStruct
        ├── CartMapper.java
        ├── OrderMapper.java
        ├── InvoiceMapper.java
        ├── ContractMapper.java
        └── PaymentMapper.java
```

## ✅ Statut d'Implémentation

- ✅ **Cart Interface Service** - Complet
- ✅ **Order Interface Service** - Complet  
- ✅ **Processus de Checkout** - Complet
- ✅ **API REST** - Complet
- ✅ **Tests Unitaires** - Complet
- ✅ **Tests d'Intégration** - Complet
- ✅ **Documentation** - Complet

## 🔄 Prochaines Étapes

1. **Exécuter les tests** : `./run-tests.sh`
2. **Vérifier la couverture de code**
3. **Tester les endpoints avec Postman/curl**
4. **Intégrer avec le frontend**
5. **Déployer en environnement de test**
