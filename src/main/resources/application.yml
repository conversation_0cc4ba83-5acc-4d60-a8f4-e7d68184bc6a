# -----------------------------------------------------------
# GENERAL
# -----------------------------------------------------------
server:
  port: 8082

spring:
  application:
    name: items-app

  # -----------------------------------------------------------
  # DATASOURCE
  # -----------------------------------------------------------
  datasource:
    url: *****************************************
    username: postgres
    password: eazycar

  # -----------------------------------------------------------
  # JPA
  # -----------------------------------------------------------
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: update
    show-sql: false
    open-in-view: false

application:
  media-size:
    image-length: 5
    video-length: 2