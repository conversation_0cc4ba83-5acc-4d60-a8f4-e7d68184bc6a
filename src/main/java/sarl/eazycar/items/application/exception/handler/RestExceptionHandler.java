package sarl.eazycar.items.application.exception.handler;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import sarl.eazycar.items.application.dto.ErrorDto;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.application.exception.TechnicalErrorException;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@RestControllerAdvice
public class RestExceptionHandler extends ResponseEntityExceptionHandler {
  @ExceptionHandler({TechnicalErrorException.class, RuntimeException.class})
  public ResponseEntity<ErrorDto> handleException(final RuntimeException errorException) {
    final HttpStatus badRequest = HttpStatus.INTERNAL_SERVER_ERROR;
    final ErrorDto errorDto =
        ErrorDto.builder().code(badRequest.value()).message(errorException.getMessage()).build();
    return new ResponseEntity<>(errorDto, badRequest);
  }

  @ExceptionHandler({FunctionalErrorException.class})
  public ResponseEntity<ErrorDto> handleFunctionalEException(
      final RuntimeException errorException) {
    final HttpStatus badRequest = HttpStatus.BAD_REQUEST;
    final ErrorDto errorDto =
        ErrorDto.builder().code(badRequest.value()).message(errorException.getMessage()).build();
    return new ResponseEntity<>(errorDto, badRequest);
  }
}
