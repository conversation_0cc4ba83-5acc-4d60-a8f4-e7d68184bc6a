package sarl.eazycar.items.application.utils;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <PERSON>EMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHA<PERSON> KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
public class RequestUriApi {
  private RequestUriApi() {}

  public static final String API_ROOT = "/api/v1";

  // Catalog
  public static final String CATALOG_ENDPOINT = API_ROOT + "/catalogs";
  public static final String CATALOG_ENDPOINT_WITH_ID_VARIABLE = CATALOG_ENDPOINT + "/{catalogId}";

  // Category
  public static final String CATEGORY_ENDPOINT = API_ROOT + "/categories";
  public static final String CATEGORY_ENDPOINT_WITH_ID_VARIABLE =
      CATEGORY_ENDPOINT + "/{categoryId}";

  // Car
  public static final String CAR_ENDPOINT = API_ROOT + "/cars";
  public static final String CAR_ENDPOINT_WITH_ID_VARIABLE = CAR_ENDPOINT + "/{carId}";
  public static final String CAR_ENDPOINT_WITH_SEARCH_VARIABLE = CAR_ENDPOINT + "/search";

  // Article Option
  public static final String ARTICLE_OPTION_ENDPOINT = API_ROOT + "/article-options";
  public static final String ARTICLE_OPTION_ENDPOINT_WITH_ID_VARIABLE =
      ARTICLE_OPTION_ENDPOINT + "/{articleOptionId}";

  // Image & Video
  public static final String IMAGE_ENDPOINT = API_ROOT + "/images";
  public static final String VIDEO_ENDPOINT = API_ROOT + "/videos";
}
