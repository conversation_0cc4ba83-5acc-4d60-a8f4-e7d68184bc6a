package sarl.eazycar.items.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sarl.eazycar.items.domain.entity.enums.ContractStatus;

import java.time.LocalDateTime;

/**
 * DTO pour Contract
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractDto {
    
    private String contractId;
    
    private String documentUrl;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime signatureDate;
    
    private ContractStatus status;
    
    private OrderDto order;
}
