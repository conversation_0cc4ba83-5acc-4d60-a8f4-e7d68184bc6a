package sarl.eazycar.items.application.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** DTO for {@link sarl.eazycar.items.domain.entity.Image} */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ImageDto implements Serializable {
  private String fileId;
  @NotBlank private String name;
  @NotBlank private String path;
  @NotBlank private String format;
  private int size;
  @JsonIgnore private ArticleDto article;
}
