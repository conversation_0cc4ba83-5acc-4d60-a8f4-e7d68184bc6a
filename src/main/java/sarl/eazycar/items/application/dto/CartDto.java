package sarl.eazycar.items.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sarl.eazycar.items.domain.entity.enums.CartStatus;

import java.math.BigDecimal;
import java.util.List;

/**
 * DTO pour Cart
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CartDto {
    
    private String cartId;
    
    private CartStatus status;
    
    private BigDecimal totalAmount;
    
    private String accountId;
    
    private List<OrderDto> orders;
}
