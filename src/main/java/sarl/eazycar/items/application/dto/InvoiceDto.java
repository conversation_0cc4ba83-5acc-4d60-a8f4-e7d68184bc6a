package sarl.eazycar.items.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sarl.eazycar.items.domain.entity.enums.InvoiceStatus;

/** DTO pour Invoice */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceDto {

  private String invoiceId;

  @JsonFormat(pattern = "yyyy-MM-dd")
  private LocalDate issueDate;

  @JsonFormat(pattern = "yyyy-MM-dd")
  private LocalDate dueDate;

  private InvoiceStatus status;

  private OrderDto order;

  private List<PaymentDto> payments;
}
