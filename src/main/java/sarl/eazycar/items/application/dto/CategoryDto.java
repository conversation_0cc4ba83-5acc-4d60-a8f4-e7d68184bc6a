package sarl.eazycar.items.application.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** DTO for {@link sarl.eazycar.items.domain.entity.Category} */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CategoryDto implements Serializable {
  private String categoryId;
  @NotBlank private String type;
  @NotBlank private String label;
  private String description;
  @NotNull private BigDecimal minPrice;
  @NotNull private BigDecimal maxPrice;
}
