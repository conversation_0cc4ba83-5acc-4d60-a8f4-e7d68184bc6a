package sarl.eazycar.items.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sarl.eazycar.items.domain.entity.enums.PaymentMethod;
import sarl.eazycar.items.domain.entity.enums.PaymentStatus;

/** DTO pour Payment */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentDto {

  private String paymentId;

  @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
  private LocalDateTime paymentDate;

  private BigDecimal amount;

  private PaymentMethod paymentMethod;

  private String transactionId;

  private PaymentStatus status;

  private InvoiceDto invoice;
}
