package sarl.eazycar.items.application.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** DTO for {@link sarl.eazycar.items.domain.entity.Catalog} */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CatalogDto implements Serializable {
  private String catalogId;
  @NotBlank private String code;
  @NotBlank private String service;
  private String description;
  @JsonIgnore private List<PriceDto> prices;
}
