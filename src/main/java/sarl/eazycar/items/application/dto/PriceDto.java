package sarl.eazycar.items.application.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** DTO for {@link sarl.eazycar.items.domain.entity.Price} */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PriceDto implements Serializable {
  private String priceId;
  private BigDecimal price;
  private String currency;
  private BigDecimal discountPercent;
  @JsonIgnore private ArticleDto article;
  private CatalogDto catalog;
  private ArticleOptionDto articleOption;
}
