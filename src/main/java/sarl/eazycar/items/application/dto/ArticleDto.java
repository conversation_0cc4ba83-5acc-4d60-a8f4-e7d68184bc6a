package sarl.eazycar.items.application.dto;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** DTO for {@link sarl.eazycar.items.domain.entity.Article} */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ArticleDto implements Serializable {
  private String articleId;
  private String name;
  private String description;
  private String cityLocation;
  private String districtLocation;
  private boolean available;
  private boolean visible;
  private List<PriceDto> prices;
  private List<VideoDto> videos;
  private List<ImageDto> images;
  private CategoryDto category;
}
