package sarl.eazycar.items.application.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** DTO for {@link sarl.eazycar.items.domain.entity.Car} */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CarDto implements Serializable {
  private String carId;
  private String brand;
  private String model;
  private Integer factoryYear;
  private ArticleDto article;
}
