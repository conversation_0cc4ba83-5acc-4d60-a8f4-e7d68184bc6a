package sarl.eazycar.items.application.dto.criteria;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springdoc.core.annotations.ParameterObject;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 6/5/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 6/5/25
 * @project : EazyCar
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ParameterObject
@ToString
public class CarSearchCriteria implements Serializable {
  private String catalogCode;
  private String carBrand;
  private String carModel;
  private String locationCity;
  private String locationDistrict;
  private String categoryType;
  private Integer minFactoryYear;
  private Integer maxFactoryYear;
  private BigDecimal minPrice;
  private BigDecimal maxPrice;
  private String currency;
  @Builder.Default private boolean availableOnly = true;
  private List<String> articleOptions;
}
