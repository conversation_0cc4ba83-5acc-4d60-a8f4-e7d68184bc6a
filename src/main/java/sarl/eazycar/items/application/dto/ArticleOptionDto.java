package sarl.eazycar.items.application.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** DTO for {@link sarl.eazycar.items.domain.entity.ArticleOption} */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ArticleOptionDto implements Serializable {
  private String optionId;
  @NotBlank private String optionName;
  private String optionDescription;
  @JsonIgnore private List<PriceDto> prices;
}
