package sarl.eazycar.items.application.rest;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import sarl.eazycar.items.application.dto.CategoryDto;
import sarl.eazycar.items.application.rest.api.CategoryApi;
import sarl.eazycar.items.domain.service.ICategoryService;
import sarl.eazycar.items.infrastructure.mapper.CategoryMapper;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@RequiredArgsConstructor
@RestController
public class CategoryResource implements CategoryApi {
  private final ICategoryService categoryService;

  @Override
  public ResponseEntity<CategoryDto> createCategory(CategoryDto dto) {
    return ResponseEntity.ok(
        CategoryMapper.INSTANCE.toDto(
            categoryService.createCategory(CategoryMapper.INSTANCE.toEntity(dto))));
  }

  @Override
  public ResponseEntity<CategoryDto> updateCategory(String categoryId, CategoryDto dto) {
    return ResponseEntity.ok(
        CategoryMapper.INSTANCE.toDto(
            categoryService.updateCategory(categoryId, CategoryMapper.INSTANCE.toEntity(dto))));
  }

  @Override
  public ResponseEntity<CategoryDto> getCategoryById(String categoryId) {
    return ResponseEntity.ok(
        CategoryMapper.INSTANCE.toDto(categoryService.getCategoryById(categoryId)));
  }

  @Override
  public ResponseEntity<List<CategoryDto>> getAllCategories() {
    return ResponseEntity.ok(CategoryMapper.INSTANCE.toDto(categoryService.getAllCategories()));
  }
}
