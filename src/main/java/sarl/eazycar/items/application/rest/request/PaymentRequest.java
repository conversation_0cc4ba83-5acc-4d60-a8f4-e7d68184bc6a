package sarl.eazycar.items.application.rest.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import sarl.eazycar.items.domain.entity.enums.PaymentMethod;

import java.math.BigDecimal;

/**
 * Request pour Payment
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentRequest {
    
    private BigDecimal amount;
    
    private PaymentMethod paymentMethod;
    
    private String transactionId;
}
