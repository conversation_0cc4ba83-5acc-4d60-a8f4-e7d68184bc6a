package sarl.eazycar.items.application.rest;

import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import sarl.eazycar.items.application.dto.CarDto;
import sarl.eazycar.items.application.dto.criteria.CarSearchCriteria;
import sarl.eazycar.items.application.rest.api.CarApi;
import sarl.eazycar.items.application.rest.request.CarRequest;
import sarl.eazycar.items.application.utils.PaginationUtil;
import sarl.eazycar.items.domain.service.ICarService;
import sarl.eazycar.items.infrastructure.factory.CarFactory;
import sarl.eazycar.items.infrastructure.mapper.CarMapper;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@RequiredArgsConstructor
@Slf4j
@RestController
public class CarResource implements CarApi {
  private final ICarService carService;
  private final CarFactory carFactory;

  @Override
  public ResponseEntity<CarDto> createCar(CarRequest request) {
    return ResponseEntity.ok(
        CarMapper.INSTANCE.toDto(carService.createCar(carFactory.buildCar(request))));
  }

  @Override
  public ResponseEntity<CarDto> updateCar(String carId, CarRequest request) {
    return ResponseEntity.ok(
        CarMapper.INSTANCE.toDto(carService.updateCar(carId, carFactory.buildCar(request))));
  }

  @Override
  public ResponseEntity<CarDto> getCarById(String carId) {
    return ResponseEntity.ok(CarMapper.INSTANCE.toDto(carService.getCarById(carId)));
  }

  @Override
  public ResponseEntity<List<CarDto>> getAllCars() {
    return ResponseEntity.ok(CarMapper.INSTANCE.toDto(carService.getAllCars()));
  }

  @Override
  public ResponseEntity<List<CarDto>> searchCars(CarSearchCriteria criteria, Pageable pageable) {
    log.debug("REST request to search Cars by criteria: {}", criteria);
    Page<CarDto> page = carService.searchCars(criteria, pageable).map(CarMapper.INSTANCE::toDto);
    HttpHeaders headers =
        PaginationUtil.generatePaginationHttpHeaders(
            ServletUriComponentsBuilder.fromCurrentRequest(), page);
    return ResponseEntity.ok().headers(headers).body(page.getContent());
  }
}
