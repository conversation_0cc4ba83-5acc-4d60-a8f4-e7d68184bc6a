package sarl.eazycar.items.application.rest.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OptionRequest implements Serializable {
  private String priceId;
  @NotBlank private String articleOptionId;
  @NotBlank private String catalogId;
  @NotNull private BigDecimal price;
  @NotBlank private String currency;
  @NotNull private BigDecimal discountPercent;
}
