package sarl.eazycar.items.application.rest;

import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import sarl.eazycar.items.application.dto.ImageDto;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.application.rest.api.ImageApi;
import sarl.eazycar.items.application.rest.request.ImageRequest;
import sarl.eazycar.items.domain.service.impl.ImageService;
import sarl.eazycar.items.infrastructure.factory.ImageFactory;
import sarl.eazycar.items.infrastructure.mapper.ImageMapper;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/4/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/4/25
 * @project : EazyCar
 */
@RequiredArgsConstructor
@RestController
public class ImageResource implements ImageApi {
  private final ImageService imageService;
  private final ImageFactory imageFactory;

  @Override
  public ResponseEntity<List<ImageDto>> createImages(ImageRequest request) {
    if (Objects.isNull(request)) {
      throw new FunctionalErrorException("Image request must be provided");
    }
    return ResponseEntity.ok(
        ImageMapper.INSTANCE.toDto(
            imageService.createImage(imageFactory.buildImage(request), request.getArticleId())));
  }
}
