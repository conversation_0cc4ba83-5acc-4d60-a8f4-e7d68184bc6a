package sarl.eazycar.items.application.rest;

import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import sarl.eazycar.items.application.dto.VideoDto;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.application.rest.api.VideoApi;
import sarl.eazycar.items.application.rest.request.VideoRequest;
import sarl.eazycar.items.domain.service.IVideoService;
import sarl.eazycar.items.infrastructure.factory.VideoFactory;
import sarl.eazycar.items.infrastructure.mapper.VideoMapper;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/4/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/4/25
 * @project : EazyCar
 */
@RequiredArgsConstructor
@RestController
public class VideoResource implements VideoApi {
  private final IVideoService videoService;
  private final VideoFactory videoFactory;

  @Override
  public ResponseEntity<List<VideoDto>> createVideos(VideoRequest request) {
    if (Objects.isNull(request)) {
      throw new FunctionalErrorException("Video request must be provided");
    }
    return ResponseEntity.ok(
        VideoMapper.INSTANCE.toDto(
            videoService.createVideo(videoFactory.buildVideo(request), request.getArticleId())));
  }
}
