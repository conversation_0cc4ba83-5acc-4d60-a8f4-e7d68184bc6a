package sarl.eazycar.items.application.rest.api;

import static sarl.eazycar.items.application.utils.RequestUriApi.CAR_ENDPOINT;
import static sarl.eazycar.items.application.utils.RequestUriApi.CAR_ENDPOINT_WITH_ID_VARIABLE;
import static sarl.eazycar.items.application.utils.RequestUriApi.CAR_ENDPOINT_WITH_SEARCH_VARIABLE;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import sarl.eazycar.items.application.dto.CarDto;
import sarl.eazycar.items.application.dto.criteria.CarSearchCriteria;
import sarl.eazycar.items.application.rest.request.CarRequest;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@Tag(name = "Car")
public interface CarApi {
  @PostMapping(
      value = CAR_ENDPOINT,
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Create a car", description = "This method allows to create a car")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Car created successfully."),
        @ApiResponse(responseCode = "500", description = "Problems during creation of car.")
      })
  ResponseEntity<CarDto> createCar(@Valid @RequestBody CarRequest request);

  @PutMapping(
      value = CAR_ENDPOINT_WITH_ID_VARIABLE,
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Update a car",
      description = "This method allows to update the car knowing by its carId")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Car updated successfully."),
        @ApiResponse(responseCode = "500", description = "Problems during update of car.")
      })
  ResponseEntity<CarDto> updateCar(
      @PathVariable("carId") String carId, @Valid @RequestBody CarRequest request);

  @GetMapping(value = CAR_ENDPOINT_WITH_ID_VARIABLE, produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Get the car by carId.",
      description = "This method allows to get the car knowing by its carId")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Car retrieved successfully."),
        @ApiResponse(responseCode = "500", description = "Problems during retrieval of car.")
      })
  ResponseEntity<CarDto> getCarById(@PathVariable("carId") String carId);

  @GetMapping(value = CAR_ENDPOINT, produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Get All the cars", description = "This method allows to get all the cars")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Car retrieve successfully."),
        @ApiResponse(
            responseCode = "500",
            description = "Problems when during retrieval of all the car.")
      })
  ResponseEntity<List<CarDto>> getAllCars();

  @GetMapping(
      value = CAR_ENDPOINT_WITH_SEARCH_VARIABLE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Search cars", description = "This method allows to search cars")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Search car operation successfully done."),
        @ApiResponse(responseCode = "500", description = "Problems during search car operation.")
      })
  ResponseEntity<List<CarDto>> searchCars(
      CarSearchCriteria criteria, @ParameterObject Pageable pageable);
}
