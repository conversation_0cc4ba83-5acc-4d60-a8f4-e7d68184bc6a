package sarl.eazycar.items.application.rest.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import sarl.eazycar.items.application.dto.ContractDto;
import sarl.eazycar.items.application.dto.InvoiceDto;
import sarl.eazycar.items.application.dto.OrderDto;
import sarl.eazycar.items.application.dto.PaymentDto;
import sarl.eazycar.items.application.rest.request.PaymentRequest;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;

import java.time.LocalDateTime;
import java.util.List;

/**
 * REST API for order management
 */
@Tag(name = "Order", description = "Order management API")
@RequestMapping("/api/v1/orders")
public interface OrderApi {

    @Operation(summary = "Get the list of orders for a user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Order list retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/account/{accountId}")
    ResponseEntity<List<OrderDto>> getOrdersByAccountId(
        @Parameter(description = "User account ID", required = true)
        @PathVariable String accountId
    );

    @Operation(summary = "Get details of a specific order")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Order details retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Order not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/{orderId}")
    ResponseEntity<OrderDto> getOrderById(
        @Parameter(description = "Order ID", required = true)
        @PathVariable String orderId
    );

    @Operation(summary = "Update the status of an order")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Order status updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid status transition"),
        @ApiResponse(responseCode = "404", description = "Order not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PutMapping("/{orderId}/status")
    ResponseEntity<OrderDto> updateOrderStatus(
        @Parameter(description = "Order ID", required = true)
        @PathVariable String orderId,
        @Parameter(description = "New status", required = true)
        @RequestParam OrderStatus status
    );

    @Operation(summary = "Get the invoice associated with an order")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Invoice retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Invoice not found for this order"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/{orderId}/invoice")
    ResponseEntity<InvoiceDto> getInvoiceByOrderId(
        @Parameter(description = "Order ID", required = true)
        @PathVariable String orderId
    );

    @Operation(summary = "Get the list of payments for an invoice")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payment list retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Invoice not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/{orderId}/invoice/payments")
    ResponseEntity<List<PaymentDto>> getPaymentsByOrderId(
        @Parameter(description = "Order ID", required = true)
        @PathVariable String orderId
    );

    @Operation(summary = "Record a payment for an invoice")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Payment recorded successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid payment parameters"),
        @ApiResponse(responseCode = "404", description = "Invoice not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/{orderId}/invoice/payments")
    ResponseEntity<PaymentDto> recordPayment(
        @Parameter(description = "Order ID", required = true)
        @PathVariable String orderId,
        @RequestBody PaymentRequest paymentRequest
    );

    @Operation(summary = "Get the contract associated with an order")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Contract retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Contract not found for this order"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/{orderId}/contract")
    ResponseEntity<ContractDto> getContractByOrderId(
        @Parameter(description = "Order ID", required = true)
        @PathVariable String orderId
    );

    @Operation(summary = "Record the signature of a contract")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Contract signature recorded successfully"),
        @ApiResponse(responseCode = "400", description = "Contract not signable"),
        @ApiResponse(responseCode = "404", description = "Contract not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PutMapping("/{orderId}/contract/sign")
    ResponseEntity<ContractDto> signContract(
        @Parameter(description = "Order ID", required = true)
        @PathVariable String orderId,
        @Parameter(description = "Signature date and time", required = false)
        @RequestParam(required = false) LocalDateTime signatureDate
    );
}
