package sarl.eazycar.items.application.rest.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import sarl.eazycar.items.application.dto.CartDto;
import sarl.eazycar.items.application.dto.InvoiceDto;
import sarl.eazycar.items.application.rest.request.OrderRequest;

/** REST API for cart management */
@Tag(name = "Cart", description = "Cart management API")
@RequestMapping("/api/v1/carts")
public interface CartApi {

  @Operation(summary = "Get or create an active cart for a user")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Cart retrieved or created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
      })
  @GetMapping("/{accountId}")
  ResponseEntity<CartDto> getOrCreateCart(
      @Parameter(description = "User account ID", required = true) @PathVariable
          String accountId);

  @Operation(summary = "Add an order to the cart")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Order added to cart successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters"),
        @ApiResponse(responseCode = "404", description = "Article not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
      })
  @PostMapping("/{accountId}/orders")
  ResponseEntity<CartDto> addOrderToCart(
      @Parameter(description = "User account ID", required = true) @PathVariable
          String accountId,
      @RequestBody OrderRequest orderRequest);

  @Operation(summary = "Update the quantity of an order in the cart")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Quantity updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters"),
        @ApiResponse(responseCode = "404", description = "Order not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
      })
  @PutMapping("/{accountId}/orders/{orderId}/quantity")
  ResponseEntity<CartDto> updateOrderQuantity(
      @Parameter(description = "User account ID", required = true) @PathVariable
          String accountId,
      @Parameter(description = "Order ID", required = true) @PathVariable String orderId,
      @Parameter(description = "New quantity", required = true) @RequestParam
          Integer quantity);

  @Operation(summary = "Remove an order from the cart")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Order removed successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters"),
        @ApiResponse(responseCode = "404", description = "Order not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
      })
  @DeleteMapping("/{accountId}/orders/{orderId}")
  ResponseEntity<CartDto> removeOrderFromCart(
      @Parameter(description = "User account ID", required = true) @PathVariable
          String accountId,
      @Parameter(description = "Order ID", required = true) @PathVariable String orderId);

  @Operation(summary = "Clear the cart")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Cart cleared successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters"),
        @ApiResponse(responseCode = "404", description = "Cart not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
      })
  @DeleteMapping("/{accountId}/clear")
  ResponseEntity<CartDto> clearCart(
      @Parameter(description = "User account ID", required = true) @PathVariable
          String accountId);

  @Operation(summary = "Checkout process - Finalize the cart")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Checkout completed successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid cart for checkout"),
        @ApiResponse(responseCode = "404", description = "Cart not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
      })
  @PostMapping("/{accountId}/checkout")
  ResponseEntity<List<InvoiceDto>> checkout(
      @Parameter(description = "User account ID", required = true) @PathVariable
          String accountId);
}
