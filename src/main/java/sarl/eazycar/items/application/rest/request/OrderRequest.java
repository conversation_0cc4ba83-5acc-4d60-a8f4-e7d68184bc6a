package sarl.eazycar.items.application.rest.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Request pour Order
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderRequest {
    
    private String articleId;
    
    private String priceId;
    
    private Integer quantity;
    
    private BigDecimal unitPrice;
    
    private String currency;
    
    private String orderType; // PURCHASE ou RENTAL
    
    // Champs spécifiques à Purchase
    private String shippingAddress;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate estimatedDeliveryDate;
    
    // Champs spécifiques à Rental
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime startDate;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime endDate;
    
    private String pickupLocation;
    
    private String dropoffLocation;
}
