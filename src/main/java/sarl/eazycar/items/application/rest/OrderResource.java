package sarl.eazycar.items.application.rest;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import sarl.eazycar.items.application.dto.ContractDto;
import sarl.eazycar.items.application.dto.InvoiceDto;
import sarl.eazycar.items.application.dto.OrderDto;
import sarl.eazycar.items.application.dto.PaymentDto;
import sarl.eazycar.items.application.rest.api.OrderApi;
import sarl.eazycar.items.application.rest.request.PaymentRequest;
import sarl.eazycar.items.domain.entity.Contract;
import sarl.eazycar.items.domain.entity.Invoice;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.Payment;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.service.IContractService;
import sarl.eazycar.items.domain.service.IInvoiceService;
import sarl.eazycar.items.domain.service.IOrderService;
import sarl.eazycar.items.domain.service.IPaymentService;
import sarl.eazycar.items.infrastructure.mapper.ContractMapper;
import sarl.eazycar.items.infrastructure.mapper.InvoiceMapper;
import sarl.eazycar.items.infrastructure.mapper.OrderMapper;
import sarl.eazycar.items.infrastructure.mapper.PaymentMapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Contrôleur REST pour la gestion des commandes
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class OrderResource implements OrderApi {

    private final IOrderService orderService;
    private final IInvoiceService invoiceService;
    private final IPaymentService paymentService;
    private final IContractService contractService;
    private final OrderMapper orderMapper;
    private final InvoiceMapper invoiceMapper;
    private final PaymentMapper paymentMapper;
    private final ContractMapper contractMapper;

    @Override
    public ResponseEntity<List<OrderDto>> getOrdersByAccountId(String accountId) {
        log.info("Getting orders for account: {}", accountId);
        
        List<Order> orders = orderService.getOrdersByAccountId(accountId);
        List<OrderDto> orderDtos = orders.stream()
                .map(orderMapper::toDto)
                .toList();
        
        return ResponseEntity.ok(orderDtos);
    }

    @Override
    public ResponseEntity<OrderDto> getOrderById(String orderId) {
        log.info("Getting order details for order: {}", orderId);
        
        Order order = orderService.getOrderById(orderId);
        OrderDto orderDto = orderMapper.toDto(order);
        
        return ResponseEntity.ok(orderDto);
    }

    @Override
    public ResponseEntity<OrderDto> updateOrderStatus(String orderId, OrderStatus status) {
        log.info("Updating order status for order: {}, new status: {}", orderId, status);
        
        Order order = orderService.updateOrderStatus(orderId, status);
        OrderDto orderDto = orderMapper.toDto(order);
        
        return ResponseEntity.ok(orderDto);
    }

    @Override
    public ResponseEntity<InvoiceDto> getInvoiceByOrderId(String orderId) {
        log.info("Getting invoice for order: {}", orderId);
        
        Invoice invoice = invoiceService.getInvoiceByOrderId(orderId);
        InvoiceDto invoiceDto = invoiceMapper.toDto(invoice);
        
        return ResponseEntity.ok(invoiceDto);
    }

    @Override
    public ResponseEntity<List<PaymentDto>> getPaymentsByOrderId(String orderId) {
        log.info("Getting payments for order: {}", orderId);
        
        // D'abord récupérer la facture pour cette commande
        Invoice invoice = invoiceService.getInvoiceByOrderId(orderId);
        
        // Puis récupérer les paiements pour cette facture
        List<Payment> payments = paymentService.getPaymentsByInvoiceId(invoice.getInvoiceId());
        List<PaymentDto> paymentDtos = payments.stream()
                .map(paymentMapper::toDto)
                .toList();
        
        return ResponseEntity.ok(paymentDtos);
    }

    @Override
    public ResponseEntity<PaymentDto> recordPayment(String orderId, PaymentRequest paymentRequest) {
        log.info("Recording payment for order: {}", orderId);
        
        // D'abord récupérer la facture pour cette commande
        Invoice invoice = invoiceService.getInvoiceByOrderId(orderId);
        
        // Enregistrer le paiement
        Payment payment = paymentService.recordPayment(
                invoice.getInvoiceId(),
                paymentRequest.getAmount(),
                paymentRequest.getPaymentMethod(),
                paymentRequest.getTransactionId()
        );
        
        PaymentDto paymentDto = paymentMapper.toDto(payment);
        
        return ResponseEntity.status(201).body(paymentDto);
    }

    @Override
    public ResponseEntity<ContractDto> getContractByOrderId(String orderId) {
        log.info("Getting contract for order: {}", orderId);
        
        Contract contract = contractService.getContractByOrderId(orderId);
        ContractDto contractDto = contractMapper.toDto(contract);
        
        return ResponseEntity.ok(contractDto);
    }

    @Override
    public ResponseEntity<ContractDto> signContract(String orderId, LocalDateTime signatureDate) {
        log.info("Signing contract for order: {}", orderId);
        
        // Récupérer le contrat pour cette commande
        Contract contract = contractService.getContractByOrderId(orderId);
        
        // Utiliser la date fournie ou la date actuelle
        LocalDateTime dateToUse = Objects.nonNull(signatureDate) ? signatureDate : LocalDateTime.now();
        
        // Signer le contrat
        Contract signedContract = contractService.signContract(contract.getContractId(), dateToUse);
        ContractDto contractDto = contractMapper.toDto(signedContract);
        
        return ResponseEntity.ok(contractDto);
    }
}
