package sarl.eazycar.items.application.rest.api;

import static sarl.eazycar.items.application.utils.RequestUriApi.VIDEO_ENDPOINT;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import sarl.eazycar.items.application.dto.VideoDto;
import sarl.eazycar.items.application.rest.request.VideoRequest;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 4/1/25
 *
 * <AUTHOR> TECHA<PERSON> KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/4/25
 * @project : EazyCar
 */
@Tag(name = "Videos")
public interface VideoApi {

  @PostMapping(value = VIDEO_ENDPOINT, produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Create a video for article",
      description = "This method allows to create a video for article")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Video created successfully."),
        @ApiResponse(responseCode = "500", description = "Problems during creation of video.")
      })
  ResponseEntity<List<VideoDto>> createVideos(@Valid @RequestBody VideoRequest request);
}
