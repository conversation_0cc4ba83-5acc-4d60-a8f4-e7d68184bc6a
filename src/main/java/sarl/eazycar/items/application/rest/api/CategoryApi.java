package sarl.eazycar.items.application.rest.api;

import static sarl.eazycar.items.application.utils.RequestUriApi.CATEGORY_ENDPOINT;
import static sarl.eazycar.items.application.utils.RequestUriApi.CATEGORY_ENDPOINT_WITH_ID_VARIABLE;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import sarl.eazycar.items.application.dto.CategoryDto;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@Tag(name = "Category")
public interface CategoryApi {
  @PostMapping(
      value = CATEGORY_ENDPOINT,
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Create a category", description = "This method allows to create a category")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Category created successfully."),
        @ApiResponse(responseCode = "500", description = "Problems during creation of category.")
      })
  ResponseEntity<CategoryDto> createCategory(@Valid @RequestBody CategoryDto dto);

  @PutMapping(
      value = CATEGORY_ENDPOINT_WITH_ID_VARIABLE,
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Update a category",
      description = "This method allows to update the category knowing by its categoryId")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Category updated successfully."),
        @ApiResponse(responseCode = "500", description = "Problems during update of category.")
      })
  ResponseEntity<CategoryDto> updateCategory(
      @PathVariable("categoryId") String categoryId, @Valid @RequestBody CategoryDto dto);

  @GetMapping(
      value = CATEGORY_ENDPOINT_WITH_ID_VARIABLE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Get the category by categoryId.",
      description = "This method allows to get the category knowing by its categoryId")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Category retrieved successfully."),
        @ApiResponse(responseCode = "500", description = "Problems during retrieval of category.")
      })
  ResponseEntity<CategoryDto> getCategoryById(@PathVariable("categoryId") String categoryId);

  @GetMapping(value = CATEGORY_ENDPOINT, produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Get All the categories",
      description = "This method allows you to get all the categories")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Category retrieve successfully."),
        @ApiResponse(
            responseCode = "500",
            description = "Problems when during retrieval of all the categories.")
      })
  ResponseEntity<List<CategoryDto>> getAllCategories();
}
