package sarl.eazycar.items.application.rest;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import sarl.eazycar.items.application.dto.ArticleOptionDto;
import sarl.eazycar.items.application.rest.api.ArticleOptionApi;
import sarl.eazycar.items.domain.service.impl.ArticleOptionService;
import sarl.eazycar.items.infrastructure.mapper.ArticleOptionMapper;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@RequiredArgsConstructor
@RestController
public class ArticleOptionResource implements ArticleOptionApi {
  private final ArticleOptionService articleOptionService;

  @Override
  public ResponseEntity<ArticleOptionDto> createArticleOption(ArticleOptionDto dto) {
    return ResponseEntity.ok(
        ArticleOptionMapper.INSTANCE.toDto(
            articleOptionService.createArticleOption(ArticleOptionMapper.INSTANCE.toEntity(dto))));
  }

  @Override
  public ResponseEntity<ArticleOptionDto> updateArticleOption(
      String articleOptionId, ArticleOptionDto dto) {
    return ResponseEntity.ok(
        ArticleOptionMapper.INSTANCE.toDto(
            articleOptionService.updateArticleOption(
                articleOptionId, ArticleOptionMapper.INSTANCE.toEntity(dto))));
  }

  @Override
  public ResponseEntity<ArticleOptionDto> getArticleOptionById(String articleOptionId) {
    return ResponseEntity.ok(
        ArticleOptionMapper.INSTANCE.toDto(
            articleOptionService.getArticleOptionById(articleOptionId)));
  }

  @Override
  public ResponseEntity<List<ArticleOptionDto>> getAllArticleOptions() {
    return ResponseEntity.ok(
        ArticleOptionMapper.INSTANCE.toDto(articleOptionService.getAllArticleOptions()));
  }
}
