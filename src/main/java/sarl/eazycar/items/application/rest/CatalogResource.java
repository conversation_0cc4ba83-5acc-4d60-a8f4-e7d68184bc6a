package sarl.eazycar.items.application.rest;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import sarl.eazycar.items.application.dto.CatalogDto;
import sarl.eazycar.items.application.rest.api.CatalogApi;
import sarl.eazycar.items.domain.service.ICatalogService;
import sarl.eazycar.items.infrastructure.mapper.CatalogMapper;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@RequiredArgsConstructor
@RestController
public class CatalogResource implements CatalogApi {
  private final ICatalogService catalogService;

  @Override
  public ResponseEntity<CatalogDto> createCatalog(CatalogDto dto) {
    return ResponseEntity.ok(
        CatalogMapper.INSTANCE.toDto(
            catalogService.createCatalog(CatalogMapper.INSTANCE.toEntity(dto))));
  }

  @Override
  public ResponseEntity<CatalogDto> updateCatalog(String catalogId, CatalogDto dto) {
    return ResponseEntity.ok(
        CatalogMapper.INSTANCE.toDto(
            catalogService.updateCatalog(catalogId, CatalogMapper.INSTANCE.toEntity(dto))));
  }

  @Override
  public ResponseEntity<CatalogDto> getCatalogById(String catalogId) {
    return ResponseEntity.ok(
        CatalogMapper.INSTANCE.toDto(catalogService.getCatalogById(catalogId)));
  }

  @Override
  public ResponseEntity<List<CatalogDto>> getAllCatalogs() {
    return ResponseEntity.ok(CatalogMapper.INSTANCE.toDto(catalogService.getAllCatalogs()));
  }
}
