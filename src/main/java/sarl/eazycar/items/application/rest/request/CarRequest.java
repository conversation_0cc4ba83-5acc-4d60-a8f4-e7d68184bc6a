package sarl.eazycar.items.application.rest.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CarRequest implements Serializable {
  @NotBlank private String categoryId;
  @NotBlank private String brand;
  @NotBlank private String model;
  private Integer factoryYear;
  @NotBlank private String name;
  @NotBlank private String description;
  @NotBlank private String cityLocation;
  @NotBlank private String districtLocation;
  @NotNull private boolean available;
  private List<OptionRequest> options;
}
