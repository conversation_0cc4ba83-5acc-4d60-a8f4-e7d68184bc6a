package sarl.eazycar.items.application.rest.request;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import sarl.eazycar.items.application.dto.VideoDto;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/4/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/4/25
 * @project : EazyCar
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VideoRequest implements Serializable {
  @NotBlank private String articleId;
  private List<VideoDto> videos;
}
