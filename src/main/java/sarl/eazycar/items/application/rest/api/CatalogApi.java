package sarl.eazycar.items.application.rest.api;

import static sarl.eazycar.items.application.utils.RequestUriApi.CATALOG_ENDPOINT;
import static sarl.eazycar.items.application.utils.RequestUriApi.CATALOG_ENDPOINT_WITH_ID_VARIABLE;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import sarl.eazycar.items.application.dto.CatalogDto;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@Tag(name = "Catalog")
public interface CatalogApi {
  @PostMapping(
      value = CATALOG_ENDPOINT,
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Create a catalog", description = "This method allows to create a catalog")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Catalog created successfully."),
        @ApiResponse(responseCode = "500", description = "Problems during creation of catalog.")
      })
  ResponseEntity<CatalogDto> createCatalog(@Valid @RequestBody CatalogDto dto);

  @PutMapping(
      value = CATALOG_ENDPOINT_WITH_ID_VARIABLE,
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Update a catalog",
      description = "This method allows to update the catalog knowing by its catalogId")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Catalog updated successfully."),
        @ApiResponse(responseCode = "500", description = "Problems during update of catalog.")
      })
  ResponseEntity<CatalogDto> updateCatalog(
      @PathVariable("catalogId") String catalogId, @Valid @RequestBody CatalogDto dto);

  @GetMapping(
      value = CATALOG_ENDPOINT_WITH_ID_VARIABLE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Get the catalog by catalogId.",
      description = "This method allows to get the catalog knowing by its catalogId")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Catalog retrieved successfully."),
        @ApiResponse(responseCode = "500", description = "Problems during retrieval of catalog.")
      })
  ResponseEntity<CatalogDto> getCatalogById(@PathVariable("catalogId") String catalogId);

  @GetMapping(value = CATALOG_ENDPOINT, produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Get All the catalogs",
      description = "This method allows you to get all the catalogs")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Catalog retrieve successfully."),
        @ApiResponse(
            responseCode = "500",
            description = "Problems when during retrieval of all the catalog.")
      })
  ResponseEntity<List<CatalogDto>> getAllCatalogs();
}
