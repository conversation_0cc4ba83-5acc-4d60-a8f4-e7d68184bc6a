package sarl.eazycar.items.application.rest.api;

import static sarl.eazycar.items.application.utils.RequestUriApi.ARTICLE_OPTION_ENDPOINT;
import static sarl.eazycar.items.application.utils.RequestUriApi.ARTICLE_OPTION_ENDPOINT_WITH_ID_VARIABLE;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import sarl.eazycar.items.application.dto.ArticleOptionDto;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@Tag(name = "ArticleOption")
public interface ArticleOptionApi {
  @PostMapping(
      value = ARTICLE_OPTION_ENDPOINT,
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Create an article option",
      description = "This method allows to create an article option")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Article option created successfully."),
        @ApiResponse(
            responseCode = "500",
            description = "Problems during creation of article option.")
      })
  ResponseEntity<ArticleOptionDto> createArticleOption(@Valid @RequestBody ArticleOptionDto dto);

  @PutMapping(
      value = ARTICLE_OPTION_ENDPOINT_WITH_ID_VARIABLE,
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Update an article option",
      description =
          "This method allows to update the article option knowing by its articleOptionId")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Article option updated successfully."),
        @ApiResponse(
            responseCode = "500",
            description = "Problems during update of article option.")
      })
  ResponseEntity<ArticleOptionDto> updateArticleOption(
      @PathVariable("articleOptionId") String articleOptionId,
      @Valid @RequestBody ArticleOptionDto dto);

  @GetMapping(
      value = ARTICLE_OPTION_ENDPOINT_WITH_ID_VARIABLE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Get the article option by articleOptionId.",
      description = "This method allows to get the articleOption knowing by its id")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Article option retrieved successfully."),
        @ApiResponse(
            responseCode = "500",
            description = "Problems during retrieval of article option.")
      })
  ResponseEntity<ArticleOptionDto> getArticleOptionById(
      @PathVariable("articleOptionId") String articleOptionId);

  @GetMapping(value = ARTICLE_OPTION_ENDPOINT, produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Get All the article options",
      description = "This method allows you to get all the article options")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Article option retrieve successfully."),
        @ApiResponse(
            responseCode = "500",
            description = "Problems when during retrieval of all the article option.")
      })
  ResponseEntity<List<ArticleOptionDto>> getAllArticleOptions();
}
