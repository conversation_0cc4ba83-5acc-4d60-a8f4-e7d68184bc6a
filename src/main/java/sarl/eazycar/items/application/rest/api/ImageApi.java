package sarl.eazycar.items.application.rest.api;

import static sarl.eazycar.items.application.utils.RequestUriApi.IMAGE_ENDPOINT;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import sarl.eazycar.items.application.dto.ImageDto;
import sarl.eazycar.items.application.rest.request.ImageRequest;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 4/1/25
 *
 * <AUTHOR> TECHA<PERSON> KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/4/25
 * @project : EazyCar
 */
@Tag(name = "Images")
public interface ImageApi {

  @PostMapping(value = IMAGE_ENDPOINT, produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(
      summary = "Create an image for article",
      description = "This method allows to create an image for article")
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Image created successfully."),
        @ApiResponse(responseCode = "500", description = "Problems during creation of image.")
      })
  ResponseEntity<List<ImageDto>> createImages(@Valid @RequestBody ImageRequest request);
}
