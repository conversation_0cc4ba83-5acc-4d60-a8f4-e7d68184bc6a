package sarl.eazycar.items.domain.entity;

import io.hypersistence.utils.hibernate.id.Tsid;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.*;
import sarl.eazycar.items.domain.entity.enums.ContractStatus;

/** Entité Contract - Représente le contrat de vente ou de location */
@Entity
@Table(name = "CONTRACT")
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Builder
public class Contract extends AuditEntity {

  @Id
  @Tsid
  @Column(name = "CONTRACT_ID")
  private String contractId;

  @Column(name = "DOCUMENT_URL")
  private String documentUrl;

  @Column(name = "SIGNATURE_DATE")
  private LocalDateTime signatureDate;

  @Enumerated(EnumType.STRING)
  @Column(name = "STATUS")
  private ContractStatus status;

  @OneToOne
  @JoinColumn(name = "ORDER_ID")
  private Order order;
}
