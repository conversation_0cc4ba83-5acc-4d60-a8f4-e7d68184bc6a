package sarl.eazycar.items.domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
@Getter
@Setter
@JsonIgnoreProperties(
    value = {"createdBy", "createdDate", "lastModifiedBy", "lastModifiedDate"},
    allowGetters = true)
public abstract class AuditEntity implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @CreatedBy
  @Column(name = "CREATED_BY", nullable = false, length = 50, updatable = false)
  private String createdBy;

  @LastModifiedBy
  @Column(name = "LAST_MODIFIED_BY", length = 50)
  private String lastModifiedBy;

  @CreatedDate
  @Column(name = "CREATED_DATE", updatable = false)
  private Instant createdDate = Instant.now();

  @LastModifiedDate
  @Column(name = "LAST_MODIFIED_DATE")
  private Instant lastModifiedDate = Instant.now();
}
