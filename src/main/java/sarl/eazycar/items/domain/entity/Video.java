package sarl.eazycar.items.domain.entity;

import io.hypersistence.utils.hibernate.id.Tsid;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@Entity
@Table(name = "VIDEO")
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Builder
public class Video extends AuditEntity {

  @Id
  @Tsid
  @Column(name = "FILE_ID")
  private String fileId;

  @Column(name = "NAME")
  private String name;

  @Column(name = "PATH")
  private String path;

  @Column(name = "FORMAT")
  private String format;

  @Column(name = "SIZE")
  private int size;

  @ManyToOne
  @JoinColumn(name = "ARTICLE_ID")
  private Article article;
}
