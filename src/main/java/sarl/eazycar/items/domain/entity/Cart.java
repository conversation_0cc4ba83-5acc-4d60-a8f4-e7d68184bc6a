package sarl.eazycar.items.domain.entity;

import io.hypersistence.utils.hibernate.id.Tsid;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.*;
import sarl.eazycar.items.domain.entity.enums.CartStatus;

/** Entité Cart - L'entité qui représente le panier de l'utilisateur */
@Entity
@Table(name = "CART")
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Builder
public class Cart extends AuditEntity {

  @Id
  @Tsid
  @Column(name = "CART_ID")
  private String cartId;

  @Enumerated(EnumType.STRING)
  @Column(name = "STATUS")
  private CartStatus status;

  @Column(name = "TOTAL_AMOUNT", precision = 19, scale = 2)
  private BigDecimal totalAmount;

  @Column(name = "ACCOUNT_ID")
  private String accountId;

  @Builder.Default
  @OneToMany(mappedBy = "cart", cascade = CascadeType.ALL, orphanRemoval = true)
  private List<Order> orders = new ArrayList<>();
}
