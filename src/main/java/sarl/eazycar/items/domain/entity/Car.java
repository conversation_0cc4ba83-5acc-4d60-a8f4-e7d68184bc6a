package sarl.eazycar.items.domain.entity;

import io.hypersistence.utils.hibernate.id.Tsid;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@Entity
@Table(
    name = "CAR",
    indexes = {
      @Index(name = "idx_car_brand", columnList = "BRAND"),
      @Index(name = "idx_car_model", columnList = "MODEL")
    })
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Builder
@ToString
public class Car extends AuditEntity {

  @Id
  @Tsid
  @Column(name = "CAR_ID")
  private String carId;

  @Column(name = "BRAND")
  private String brand;

  @Column(name = "MODEL")
  private String model;

  @Column(name = "FACTORY_YEAR")
  private Integer factoryYear;

  @OneToOne(cascade = CascadeType.ALL)
  @JoinColumn(name = "ARTICLE_ID")
  private Article article;
}
