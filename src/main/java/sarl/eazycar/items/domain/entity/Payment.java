package sarl.eazycar.items.domain.entity;

import io.hypersistence.utils.hibernate.id.Tsid;
import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.*;
import sarl.eazycar.items.domain.entity.enums.PaymentMethod;
import sarl.eazycar.items.domain.entity.enums.PaymentStatus;

/** Entité Payment - Représente une transaction de paiement pour une facture */
@Entity
@Table(name = "PAYMENT")
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Builder
public class Payment extends AuditEntity {

  @Id
  @Tsid
  @Column(name = "PAYMENT_ID")
  private String paymentId;

  @Column(name = "PAYMENT_DATE")
  private LocalDateTime paymentDate;

  @Column(name = "AMOUNT", precision = 19, scale = 2)
  private BigDecimal amount;

  @Enumerated(EnumType.STRING)
  @Column(name = "PAYMENT_METHOD")
  private PaymentMethod paymentMethod;

  @Column(name = "TRANSACTION_ID")
  private String transactionId;

  @Enumerated(EnumType.STRING)
  @Column(name = "STATUS")
  private PaymentStatus status;

  @ManyToOne
  @JoinColumn(name = "INVOICE_ID")
  private Invoice invoice;
}
