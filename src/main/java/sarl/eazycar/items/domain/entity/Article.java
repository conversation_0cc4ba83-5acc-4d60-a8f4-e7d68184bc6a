package sarl.eazycar.items.domain.entity;

import io.hypersistence.utils.hibernate.id.Tsid;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.BatchSize;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@Entity
@Table(
    name = "ARTICLE",
    indexes = {
      @Index(name = "idx_article_city_location", columnList = "CITY_LOCATION"),
      @Index(name = "idx_article_district_location", columnList = "DISTRICT_LOCATION"),
      @Index(name = "idx_article_available", columnList = "AVAILABLE")
    })
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Builder
public class Article extends AuditEntity {

  @Id
  @Tsid
  @Column(name = "ARTICLE_ID")
  private String articleId;

  @Column(name = "NAME")
  private String name;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "CITY_LOCATION")
  private String cityLocation;

  @Column(name = "DISTRICT_LOCATION")
  private String districtLocation;

  @Column(name = "AVAILABLE")
  private boolean available;

  @Column(name = "VISIBLE")
  private boolean visible;

  @OneToMany(
      mappedBy = "article",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @Builder.Default
  private List<Price> prices = new ArrayList<>();

  @OneToMany(
      mappedBy = "article",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @BatchSize(size = 2)
  @Builder.Default
  private List<Video> videos = new ArrayList<>();

  @OneToMany(
      mappedBy = "article",
      fetch = FetchType.EAGER,
      cascade = CascadeType.ALL,
      orphanRemoval = true)
  @BatchSize(size = 5)
  @Builder.Default
  private List<Image> images = new ArrayList<>();

  @ManyToOne
  @JoinColumn(name = "CATEGORY_ID")
  private Category category;

  public void addPrice(Price price) {
    prices.add(price);
    price.setArticle(this);
  }

  public void removePrice(Price price) {
    prices.remove(price);
    price.setArticle(null);
  }

  public void addVideo(Video video) {
    videos.add(video);
    video.setArticle(this);
  }

  public void removeVideo(Video video) {
    videos.remove(video);
    video.setArticle(null);
  }

  public void addImage(Image image) {
    images.add(image);
    image.setArticle(this);
  }

  public void removeImage(Image image) {
    images.remove(image);
    image.setArticle(null);
  }
}
