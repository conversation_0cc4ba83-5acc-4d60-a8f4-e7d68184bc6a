package sarl.eazycar.items.domain.entity;

import io.hypersistence.utils.hibernate.id.Tsid;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@Entity
@Table(name = "CATEGORY", indexes = {
    @Index(name = "idx_category_type", columnList = "TYPE")
})
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Builder
public class Category extends AuditEntity {

  @Id
  @Tsid
  @Column(name = "CATEGORY_ID")
  private String categoryId;

  @Column(name = "TYPE")
  private String type;

  @Column(name = "LABEL")
  private String label;

  @Column(name = "DESCRIPTION")
  private String description;

  @Column(name = "MIN_PRICE")
  private BigDecimal minPrice;

  @Column(name = "MAX_PRICE")
  private BigDecimal maxPrice;

  @OneToMany(mappedBy = "category")
  @Builder.Default
  private List<Article> articles = new ArrayList<>();

  public void addArticle(Article article) {
    articles.add(article);
    article.setCategory(this);
  }

  public void removeArticle(Article article) {
    articles.remove(article);
    article.setCategory(null);
  }
}
