package sarl.eazycar.items.domain.entity;

import io.hypersistence.utils.hibernate.id.Tsid;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.NamedAttributeNode;
import jakarta.persistence.NamedEntityGraph;
import jakarta.persistence.NamedSubgraph;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@Entity
@Table(
    name = "ARTICLE_OPTION",
    indexes = {@Index(name = "idx_article_option_name", columnList = "OPTION_NAME")})
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Builder
@NamedEntityGraph(
    name = "ArticleOption.withPrices",
    attributeNodes = {@NamedAttributeNode(value = "prices", subgraph = "prices-subgraph")},
    subgraphs = {
      @NamedSubgraph(
          name = "prices-subgraph",
          attributeNodes = {@NamedAttributeNode("article"), @NamedAttributeNode("articleOption")})
    })
public class ArticleOption extends AuditEntity {

  @Id
  @Tsid
  @Column(name = "OPTION_ID")
  private String optionId;

  @Column(name = "OPTION_NAME")
  private String optionName;

  @Column(name = "OPTION_DESCRIPTION")
  private String optionDescription;

  @OneToMany(mappedBy = "articleOption")
  @Builder.Default
  private List<Price> prices = new ArrayList<>();

  public void addPrice(Price price) {
    prices.add(price);
    price.setArticleOption(this);
  }

  public void removePrice(Price price) {
    prices.remove(price);
    price.setArticleOption(null);
  }
}
