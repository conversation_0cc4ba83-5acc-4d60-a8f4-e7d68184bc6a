package sarl.eazycar.items.domain.entity;

import io.hypersistence.utils.hibernate.id.Tsid;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@Entity
@Table(
    name = "PRICE",
    indexes = {
      @Index(name = "idx_price_article", columnList = "ARTICLE_ID"),
      @Index(name = "idx_price_catalog", columnList = "CATALOG_ID"),
      @Index(name = "idx_price_article_option", columnList = "OPTION_ID"),
      @Index(name = "idx_price_price", columnList = "PRICE")
    })
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class Price extends AuditEntity {

  @Id
  @Tsid
  @Column(name = "PRICE_ID")
  private String priceId;

  @Column(name = "PRICE")
  private BigDecimal price;

  @Column(name = "CURRENCY")
  private String currency;

  @Column(name = "DISCOUNT")
  private BigDecimal discountPercent;

  @ManyToOne
  @JoinColumn(name = "ARTICLE_ID")
  private Article article;

  @ManyToOne
  @JoinColumn(name = "CATALOG_ID")
  private Catalog catalog;

  @ManyToOne
  @JoinColumn(name = "OPTION_ID")
  private ArticleOption articleOption;
}
