package sarl.eazycar.items.domain.entity;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.*;

/** Entité Rental /-/ hérite de Order Spécifique à la location d'un article */
@Entity
@DiscriminatorValue("RENTAL")
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
public class Rental extends Order {

  @Column(name = "START_DATE")
  private LocalDateTime startDate;

  @Column(name = "END_DATE")
  private LocalDateTime endDate;

  @Column(name = "PICKUP_LOCATION")
  private String pickupLocation;

  @Column(name = "DROPOFF_LOCATION")
  private String dropoffLocation;

  @Column(name = "TOTAL_AMOUNT", precision = 19, scale = 2)
  private BigDecimal totalAmount; // unitPrice * quantity * (endDate - startDate)
}
