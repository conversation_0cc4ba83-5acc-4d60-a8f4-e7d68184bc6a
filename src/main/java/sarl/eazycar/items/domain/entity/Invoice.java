package sarl.eazycar.items.domain.entity;

import io.hypersistence.utils.hibernate.id.Tsid;
import jakarta.persistence.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.*;
import sarl.eazycar.items.domain.entity.enums.InvoiceStatus;

/** Entité Invoice - Représente la facture associée à une commande */
@Entity
@Table(name = "INVOICE")
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@Builder
public class Invoice extends AuditEntity {

  @Id
  @Tsid
  @Column(name = "INVOICE_ID")
  private String invoiceId;

  @Column(name = "ISSUE_DATE")
  private LocalDate issueDate;

  @Column(name = "DUE_DATE")
  private LocalDate dueDate;

  @Enumerated(EnumType.STRING)
  @Column(name = "STATUS")
  private InvoiceStatus status;

  @OneToOne
  @JoinColumn(name = "ORDER_ID")
  private Order order;

  @Builder.Default
  @OneToMany(mappedBy = "invoice", cascade = CascadeType.ALL, orphanRemoval = true)
  private List<Payment> payments = new ArrayList<>();
}
