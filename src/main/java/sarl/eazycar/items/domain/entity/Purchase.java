package sarl.eazycar.items.domain.entity;

import jakarta.persistence.*;
import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Entité Purchase - hérite de Order
 * Spécifique à la vente d'un article
 */
@Entity
@DiscriminatorValue("PURCHASE")
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
public class Purchase extends Order {

    @Column(name = "SHIPPING_ADDRESS")
    private String shippingAddress;

    @Column(name = "ESTIMATED_DELIVERY_DATE")
    private LocalDate estimatedDeliveryDate;

    @Column(name = "TOTAL_AMOUNT", precision = 19, scale = 2)
    private BigDecimal totalAmount; // quantity * unitPrice
}
