package sarl.eazycar.items.domain.entity;

import io.hypersistence.utils.hibernate.id.Tsid;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.NamedAttributeNode;
import jakarta.persistence.NamedEntityGraph;
import jakarta.persistence.NamedSubgraph;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@Entity
@Table(
    name = "CATALOG",
    indexes = {@Index(name = "idx_catalog_code", columnList = "CODE")})
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@NamedEntityGraph(
    name = "Catalog.withPrices",
    attributeNodes = {@NamedAttributeNode(value = "prices", subgraph = "prices-subgraph")},
    subgraphs = {
      @NamedSubgraph(
          name = "prices-subgraph",
          attributeNodes = {@NamedAttributeNode("article"), @NamedAttributeNode("articleOption")})
    })
public class Catalog extends AuditEntity {

  @Id
  @Tsid
  @Column(name = "CATALOG_ID")
  private String catalogId;

  @Column(name = "CODE", unique = true)
  private String code;

  @Column(name = "SERVICE")
  private String service;

  @Column(name = "DESCRIPTION")
  private String description;

  @OneToMany(mappedBy = "catalog")
  @Builder.Default
  private List<Price> prices = new ArrayList<>();

  public void addPrice(Price price) {
    prices.add(price);
    price.setCatalog(this);
  }

  public void removePrice(Price price) {
    prices.remove(price);
    price.setCatalog(null);
  }
}
