package sarl.eazycar.items.domain.service;

import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Invoice;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.enums.InvoiceStatus;

import java.util.List;

/**
 * Interface pour le service Invoice
 */
public interface IInvoiceService {
    
    /**
     * Générer une facture pour une commande
     */
    Invoice generateInvoiceForOrder(Order order) throws FunctionalErrorException;
    
    /**
     * Obtenir la facture associée à une commande
     */
    Invoice getInvoiceByOrderId(String orderId) throws FunctionalErrorException;
    
    /**
     * Obtenir une facture par son ID
     */
    Invoice getInvoiceById(String invoiceId) throws FunctionalErrorException;
    
    /**
     * Mettre à jour le statut d'une facture
     */
    Invoice updateInvoiceStatus(String invoiceId, InvoiceStatus newStatus) throws FunctionalErrorException;
    
    /**
     * Obtenir les factures par statut
     */
    List<Invoice> getInvoicesByStatus(InvoiceStatus status);
    
    /**
     * Obtenir les factures en retard
     */
    List<Invoice> getOverdueInvoices();
}
