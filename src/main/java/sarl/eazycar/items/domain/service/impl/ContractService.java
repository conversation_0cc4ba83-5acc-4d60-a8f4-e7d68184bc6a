package sarl.eazycar.items.domain.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Contract;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.enums.ContractStatus;
import sarl.eazycar.items.domain.repository.ContractRepository;
import sarl.eazycar.items.domain.service.IContractService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Implémentation du service Contract
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class ContractService implements IContractService {

    private final ContractRepository contractRepository;

    @Override
    public Contract generateContractForOrder(Order order) throws FunctionalErrorException {
        if (Objects.isNull(order)) {
            throw new FunctionalErrorException("Order cannot be null");
        }
        if (Strings.isBlank(order.getOrderId())) {
            throw new FunctionalErrorException("Order ID cannot be null or empty");
        }

        // Vérifier qu'un contrat n'existe pas déjà pour cette commande
        contractRepository.findByOrderOrderId(order.getOrderId())
                .ifPresent(existingContract -> {
                    throw new FunctionalErrorException(
                        "Contract already exists for order: " + order.getOrderId());
                });

        // Générer l'URL du document (ici on simule, dans un vrai projet ce serait généré)
        String documentUrl = generateContractDocumentUrl(order);

        Contract contract = Contract.builder()
                .documentUrl(documentUrl)
                .status(ContractStatus.PENDING_SIGNATURE)
                .order(order)
                .build();

        return contractRepository.save(contract);
    }

    @Override
    @Transactional(readOnly = true)
    public Contract getContractByOrderId(String orderId) throws FunctionalErrorException {
        if (Strings.isBlank(orderId)) {
            throw new FunctionalErrorException("Order ID cannot be null or empty");
        }

        return contractRepository.findByOrderOrderId(orderId)
                .orElseThrow(() -> new FunctionalErrorException("Contract not found for order: " + orderId));
    }

    @Override
    @Transactional(readOnly = true)
    public Contract getContractById(String contractId) throws FunctionalErrorException {
        if (Strings.isBlank(contractId)) {
            throw new FunctionalErrorException("Contract ID cannot be null or empty");
        }

        return contractRepository.findById(contractId)
                .orElseThrow(() -> new FunctionalErrorException("Contract not found: " + contractId));
    }

    @Override
    public Contract signContract(String contractId, LocalDateTime signatureDate) throws FunctionalErrorException {
        if (Strings.isBlank(contractId)) {
            throw new FunctionalErrorException("Contract ID cannot be null or empty");
        }
        if (Objects.isNull(signatureDate)) {
            throw new FunctionalErrorException("Signature date cannot be null");
        }

        Contract contract = getContractById(contractId);

        if (contract.getStatus() != ContractStatus.PENDING_SIGNATURE) {
            throw new FunctionalErrorException(
                "Contract is not in PENDING_SIGNATURE status. Current status: " + contract.getStatus());
        }

        contract.setSignatureDate(signatureDate);
        contract.setStatus(ContractStatus.SIGNED);

        return contractRepository.save(contract);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Contract> getContractsByStatus(ContractStatus status) {
        if (Objects.isNull(status)) {
            throw new FunctionalErrorException("Status cannot be null");
        }

        return contractRepository.findByStatus(status);
    }

    private String generateContractDocumentUrl(Order order) {
        // Dans un vrai projet, ceci générerait un document PDF et retournerait l'URL
        // Pour la démonstration, on retourne une URL simulée
        return String.format("https://contracts.eazycar.com/contracts/%s.pdf", order.getOrderId());
    }
}
