package sarl.eazycar.items.domain.service;

import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Contract;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.enums.ContractStatus;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Interface pour le service Contract
 */
public interface IContractService {
    
    /**
     * Générer un contrat pour une commande
     */
    Contract generateContractForOrder(Order order) throws FunctionalErrorException;
    
    /**
     * Obtenir le contrat associé à une commande
     */
    Contract getContractByOrderId(String orderId) throws FunctionalErrorException;
    
    /**
     * Obtenir un contrat par son ID
     */
    Contract getContractById(String contractId) throws FunctionalErrorException;
    
    /**
     * Enregistrer la signature d'un contrat
     */
    Contract signContract(String contractId, LocalDateTime signatureDate) throws FunctionalErrorException;
    
    /**
     * Obtenir les contrats par statut
     */
    List<Contract> getContractsByStatus(ContractStatus status);
}
