package sarl.eazycar.items.domain.service.impl;

import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Catalog;
import sarl.eazycar.items.domain.repository.CatalogRepository;
import sarl.eazycar.items.domain.service.ICatalogService;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@Service
@RequiredArgsConstructor
@Transactional
public class CatalogService implements ICatalogService {

  private final CatalogRepository catalogRepository;

  @Override
  public Catalog createCatalog(Catalog catalog) {
    if (Objects.nonNull(catalog.getCatalogId())) {
      throw new FunctionalErrorException("A new catalog cannot already have an ID.");
    }
    catalogRepository
        .findCatalogByCodeIgnoreCase(catalog.getCode())
        .ifPresent(
            cat -> {
              throw new FunctionalErrorException(
                  String.format("The catalog code %s already exists.", catalog.getCode()));
            });
    return catalogRepository.save(catalog);
  }

  @Override
  public Catalog getCatalogById(String catalogId) {
    return catalogRepository
        .findById(catalogId)
        .orElseThrow(
            () -> new FunctionalErrorException("Catalog with id " + catalogId + " not found"));
  }

  @Override
  public Catalog getCatalogByCode(String catalogCode) {
    return catalogRepository
        .findCatalogByCodeIgnoreCase(catalogCode)
        .orElseThrow(
            () -> new FunctionalErrorException("Catalog with name " + catalogCode + " not found"));
  }

  @Override
  public Catalog updateCatalog(String catalogId, Catalog catalog) {
    if (Strings.isBlank(catalogId)) {
      throw new FunctionalErrorException("Invalid id");
    }
    Catalog existingCatalog = getCatalogById(catalogId);
    if (!existingCatalog.getCode().equalsIgnoreCase(catalog.getCode())) {
      catalogRepository
          .findCatalogByCodeIgnoreCase(catalog.getCode())
          .ifPresent(
              cat -> {
                throw new FunctionalErrorException(
                    String.format("The catalog code %s already exists.", catalog.getCode()));
              });
    }
    existingCatalog.setCode(catalog.getCode());
    existingCatalog.setService(catalog.getService());
    existingCatalog.setDescription(catalog.getDescription());
    return catalogRepository.save(existingCatalog);
  }

  @Override
  public List<Catalog> getAllCatalogs() {
    return catalogRepository.findAll();
  }
}
