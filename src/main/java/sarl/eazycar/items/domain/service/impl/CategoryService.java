package sarl.eazycar.items.domain.service.impl;

import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Category;
import sarl.eazycar.items.domain.repository.CategoryRepository;
import sarl.eazycar.items.domain.service.ICategoryService;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@Service
@RequiredArgsConstructor
public class CategoryService implements ICategoryService {

  private final CategoryRepository categoryRepository;

  @Override
  public Category createCategory(Category category) {
    if (Objects.nonNull(category.getCategoryId())) {
      throw new FunctionalErrorException("A new category cannot already have an ID.");
    }
    if (category.getMinPrice().compareTo(category.getMaxPrice()) > 0) {
      throw new FunctionalErrorException("The min price must be less than the max price.");
    }
    categoryRepository
        .findCategoryByTypeIgnoreCase(category.getType())
        .ifPresent(
            cat -> {
              throw new FunctionalErrorException(
                  String.format("The category type %s already exists.", category.getType()));
            });
    return categoryRepository.save(category);
  }

  @Override
  public Category getCategoryByType(String type) {
    return categoryRepository
        .findCategoryByTypeIgnoreCase(type)
        .orElseThrow(
            () -> new FunctionalErrorException("Category with type " + type + " not found"));
  }

  @Override
  public Category getCategoryById(String categoryId) {
    return categoryRepository
        .findById(categoryId)
        .orElseThrow(
            () -> new FunctionalErrorException("Category with id " + categoryId + " not found"));
  }

  @Override
  public Category updateCategory(String categoryId, Category category) {
    if (Strings.isBlank(categoryId)) {
      throw new FunctionalErrorException("Invalid id");
    }
    if (category.getMinPrice().compareTo(category.getMaxPrice()) > 0) {
      throw new FunctionalErrorException("The min price must be less than the max price.");
    }
    Category existingCategory = getCategoryById(categoryId);

    if (!existingCategory.getType().equalsIgnoreCase(category.getType())) {
      categoryRepository
          .findCategoryByTypeIgnoreCase(category.getType())
          .ifPresent(
              cat -> {
                throw new FunctionalErrorException(
                    String.format("The category type %s already exists.", category.getType()));
              });
    }
    existingCategory.setType(category.getType());
    existingCategory.setLabel(category.getLabel());
    existingCategory.setDescription(category.getDescription());
    existingCategory.setMinPrice(category.getMinPrice());
    existingCategory.setMaxPrice(category.getMaxPrice());

    return categoryRepository.save(existingCategory);
  }

  @Override
  public List<Category> getAllCategories() {
    return categoryRepository.findAll();
  }
}
