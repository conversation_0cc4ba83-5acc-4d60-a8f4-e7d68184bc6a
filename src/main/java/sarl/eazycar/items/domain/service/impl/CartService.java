package sarl.eazycar.items.domain.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Cart;
import sarl.eazycar.items.domain.entity.Order;

import sarl.eazycar.items.domain.entity.Rental;
import sarl.eazycar.items.domain.entity.enums.CartStatus;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.repository.CartRepository;
import sarl.eazycar.items.domain.repository.OrderRepository;
import sarl.eazycar.items.domain.service.ICartService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Implémentation du service Cart
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class CartService implements ICartService {

    private final CartRepository cartRepository;
    private final OrderRepository orderRepository;

    @Override
    public Cart getOrCreateActiveCart(String accountId) {
        if (Strings.isBlank(accountId)) {
            throw new FunctionalErrorException("Account ID cannot be null or empty");
        }

        return cartRepository.findByAccountIdAndStatus(accountId, CartStatus.ACTIVE)
                .stream()
                .findFirst()
                .orElseGet(() -> createNewCart(accountId));
    }

    @Override
    public Cart addOrderToCart(String accountId, Order order) throws FunctionalErrorException {
        if (Strings.isBlank(accountId)) {
            throw new FunctionalErrorException("Account ID cannot be null or empty");
        }
        if (Objects.isNull(order)) {
            throw new FunctionalErrorException("Order cannot be null");
        }

        Cart cart = getOrCreateActiveCart(accountId);

        // Configurer l'ordre
        order.setAccountId(accountId);
        order.setCart(cart);
        order.setStatus(OrderStatus.IN_CART);
        order.setOrderDate(LocalDateTime.now());

        // Sauvegarder l'ordre
        Order savedOrder = orderRepository.save(order);

        // Recalculer le total du panier
        return calculateCartTotal(cart.getCartId());
    }

    @Override
    @Transactional(readOnly = true)
    public Cart getCartWithOrders(String accountId) throws FunctionalErrorException {
        if (Strings.isBlank(accountId)) {
            throw new FunctionalErrorException("Account ID cannot be null or empty");
        }

        return cartRepository.findByAccountIdAndStatus(accountId, CartStatus.ACTIVE)
                .stream()
                .findFirst()
                .orElseThrow(() -> new FunctionalErrorException("No active cart found for account: " + accountId));
    }

    @Override
    public Cart updateOrderQuantity(String accountId, String orderId, Integer newQuantity) throws FunctionalErrorException {
        if (Strings.isBlank(accountId)) {
            throw new FunctionalErrorException("Account ID cannot be null or empty");
        }
        if (Strings.isBlank(orderId)) {
            throw new FunctionalErrorException("Order ID cannot be null or empty");
        }
        if (Objects.isNull(newQuantity) || newQuantity <= 0) {
            throw new FunctionalErrorException("Quantity must be greater than 0");
        }

        Cart cart = getCartWithOrders(accountId);
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new FunctionalErrorException("Order not found: " + orderId));

        // Vérifier que l'ordre appartient au panier de l'utilisateur
        if (!order.getCart().getCartId().equals(cart.getCartId())) {
            throw new FunctionalErrorException("Order does not belong to user's cart");
        }

        order.setQuantity(newQuantity);
        orderRepository.save(order);

        return calculateCartTotal(cart.getCartId());
    }

    @Override
    public Cart removeOrderFromCart(String accountId, String orderId) throws FunctionalErrorException {
        if (Strings.isBlank(accountId)) {
            throw new FunctionalErrorException("Account ID cannot be null or empty");
        }
        if (Strings.isBlank(orderId)) {
            throw new FunctionalErrorException("Order ID cannot be null or empty");
        }

        Cart cart = getCartWithOrders(accountId);
        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new FunctionalErrorException("Order not found: " + orderId));

        // Vérifier que l'ordre appartient au panier de l'utilisateur
        if (!order.getCart().getCartId().equals(cart.getCartId())) {
            throw new FunctionalErrorException("Order does not belong to user's cart");
        }

        orderRepository.delete(order);

        return calculateCartTotal(cart.getCartId());
    }

    @Override
    public Cart clearCart(String accountId) throws FunctionalErrorException {
        if (Strings.isBlank(accountId)) {
            throw new FunctionalErrorException("Account ID cannot be null or empty");
        }

        Cart cart = getCartWithOrders(accountId);

        // Supprimer tous les ordres du panier
        orderRepository.deleteAll(cart.getOrders());

        // Mettre à jour le total
        cart.setTotalAmount(BigDecimal.ZERO);
        return cartRepository.save(cart);
    }

    @Override
    @Transactional(readOnly = true)
    public Cart getCartById(String cartId) throws FunctionalErrorException {
        if (Strings.isBlank(cartId)) {
            throw new FunctionalErrorException("Cart ID cannot be null or empty");
        }

        return cartRepository.findById(cartId)
                .orElseThrow(() -> new FunctionalErrorException("Cart not found: " + cartId));
    }

    @Override
    public Cart calculateCartTotal(String cartId) throws FunctionalErrorException {
        Cart cart = getCartById(cartId);

        BigDecimal total = cart.getOrders().stream()
                .map(this::calculateOrderTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        cart.setTotalAmount(total);
        return cartRepository.save(cart);
    }

    private Cart createNewCart(String accountId) {
        Cart cart = Cart.builder()
                .accountId(accountId)
                .status(CartStatus.ACTIVE)
                .totalAmount(BigDecimal.ZERO)
                .build();

        return cartRepository.save(cart);
    }

    private BigDecimal calculateOrderTotal(Order order) {
        if (Objects.isNull(order.getQuantity()) || Objects.isNull(order.getUnitPrice())) {
            return BigDecimal.ZERO;
        }

        BigDecimal baseTotal = order.getUnitPrice().multiply(BigDecimal.valueOf(order.getQuantity()));

        // Pour les locations, multiplier par la durée
        if (order instanceof Rental rental) {
            if (Objects.nonNull(rental.getStartDate()) && Objects.nonNull(rental.getEndDate())) {
                long days = java.time.Duration.between(rental.getStartDate(), rental.getEndDate()).toDays();
                if (days > 0) {
                    baseTotal = baseTotal.multiply(BigDecimal.valueOf(days));
                }
            }
        }

        return baseTotal;
    }
}
