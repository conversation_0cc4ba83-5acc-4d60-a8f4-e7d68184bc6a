package sarl.eazycar.items.domain.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Cart;
import sarl.eazycar.items.domain.entity.Invoice;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.enums.CartStatus;
import sarl.eazycar.items.domain.entity.enums.InvoiceStatus;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.service.IArticleService;
import sarl.eazycar.items.domain.service.ICartService;
import sarl.eazycar.items.domain.service.ICheckoutService;
import sarl.eazycar.items.domain.service.IContractService;
import sarl.eazycar.items.domain.service.IInvoiceService;

/** Implémentation du service Checkout */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class CheckoutService implements ICheckoutService {

  private final ICartService cartService;
  private final IInvoiceService invoiceService;
  private final IContractService contractService;
  private final IArticleService articleService;

  @Override
  public List<Invoice> processCheckout(String accountId) throws FunctionalErrorException {
    List<Invoice> invoices = new ArrayList<>();
    if (Strings.isBlank(accountId)) {
      throw new FunctionalErrorException("Account ID cannot be null or empty");
    }

    log.info("Starting checkout process for account: {}", accountId);

    // 1. Récupérer le panier actif avec ses commandes
    Cart cart = cartService.getCartWithOrders(accountId);

    // 2. Valider le contenu du panier
    validateCartForCheckout(cart);

    // 3. Convertir les ordres du panier en commandes confirmées
    cart.getOrders()
        .forEach(
            order -> {
              order.setStatus(OrderStatus.CONFIRMED);
              order.setConfirmationDate(LocalDateTime.now());
            });

    // 4. Marquer le panier comme traité
    cart.setStatus(CartStatus.CHECKED_OUT);
    cartService.calculateCartTotal(cart.getCartId());

    // 5. Générer une facture pour la première commande (ou on pourrait créer une facture globale)
    cart.getOrders().forEach(order -> invoices.add(invoiceService.generateInvoiceForOrder(order)));

    // 6. Mettre la facture en statut SENT (prête à être payée)
    invoices.forEach(
        invoice -> invoiceService.updateInvoiceStatus(invoice.getInvoiceId(), InvoiceStatus.SENT));

    // 7. Générer un contrat pour la commande
    cart.getOrders().forEach(contractService::generateContractForOrder);

    log.info(
        "Checkout completed successfully for account: {}. Invoice number: {}",
        accountId,
        invoices.size());

    return invoices;
  }

  @Override
  public void validateCartForCheckout(Cart cart) throws FunctionalErrorException {
    if (Objects.isNull(cart)) {
      throw new FunctionalErrorException("Cart cannot be null");
    }

    if (cart.getStatus() != CartStatus.ACTIVE) {
      throw new FunctionalErrorException("Cart is not active");
    }

    if (cart.getOrders().isEmpty()) {
      throw new FunctionalErrorException("Cart is empty");
    }

    // Valider chaque commande dans le panier
    for (Order order : cart.getOrders()) {
      validateOrderForCheckout(order);
    }

    // Vérifier que le montant total est cohérent
    if (Objects.isNull(cart.getTotalAmount())
        || cart.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
      throw new FunctionalErrorException("Cart total amount is invalid");
    }
  }

  private void validateOrderForCheckout(Order order) throws FunctionalErrorException {
    // Vérifier que l'ordre a tous les champs requis
    if (Objects.isNull(order.getArticle())) {
      throw new FunctionalErrorException("Order must have an associated article");
    }

    if (Objects.isNull(order.getQuantity()) || order.getQuantity() <= 0) {
      throw new FunctionalErrorException("Order quantity must be greater than 0");
    }

    if (Objects.isNull(order.getUnitPrice())
        || order.getUnitPrice().compareTo(BigDecimal.ZERO) <= 0) {
      throw new FunctionalErrorException("Order unit price must be greater than 0");
    }

    // Vérifier la disponibilité de l'article
    if (!order.getArticle().isAvailable()) {
      throw new FunctionalErrorException(
          "Article is not available: " + order.getArticle().getName());
    }

    // Vérifier que l'article existe toujours
    try {
      articleService.getArticleById(order.getArticle().getArticleId());
    } catch (FunctionalErrorException e) {
      throw new FunctionalErrorException(
          "Article no longer exists: " + order.getArticle().getArticleId());
    }
  }
}
