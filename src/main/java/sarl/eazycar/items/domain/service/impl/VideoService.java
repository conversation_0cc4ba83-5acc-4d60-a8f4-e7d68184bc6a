package sarl.eazycar.items.domain.service.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Video;
import sarl.eazycar.items.domain.repository.VideoRepository;
import sarl.eazycar.items.domain.service.IArticleService;
import sarl.eazycar.items.domain.service.IVideoService;
import sarl.eazycar.items.infrastructure.config.ApplicationProperties;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/4/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/4/25
 * @project : EazyCar
 */
@Service
@RequiredArgsConstructor
@Transactional
public class VideoService implements IVideoService {
  private final VideoRepository videoRepository;
  private final IArticleService articleService;
  private final ApplicationProperties applicationProperties;

  @Override
  public List<Video> createVideo(List<Video> videos, String articleId) {
    var count = videoRepository.countByArticleArticleId(articleId);
    if (count >= applicationProperties.getMediaSize().getVideoLength()) {
      throw new FunctionalErrorException(
          "Videos must be less than " + applicationProperties.getMediaSize().getVideoLength());
    }
    Article article = articleService.getArticleById(articleId);
    videos.forEach(
        video -> {
          video.setArticle(article);
        });
    return videoRepository.saveAll(videos);
  }
}
