package sarl.eazycar.items.domain.service.impl;

import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sarl.eazycar.items.application.dto.criteria.CarSearchCriteria;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Car;
import sarl.eazycar.items.domain.entity.Category;
import sarl.eazycar.items.domain.entity.Price;
import sarl.eazycar.items.domain.repository.CarRepository;
import sarl.eazycar.items.domain.repository.specification.CarSpecification;
import sarl.eazycar.items.domain.service.IArticleOptionService;
import sarl.eazycar.items.domain.service.ICarService;
import sarl.eazycar.items.domain.service.ICatalogService;
import sarl.eazycar.items.domain.service.ICategoryService;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class CarService implements ICarService {

  private final CarRepository carRepository;
  private final IArticleOptionService articleOptionService;
  private final ICatalogService catalogService;
  private final ICategoryService categoryService;

  @Override
  public Car createCar(Car car) {
    if (Objects.nonNull(car.getCarId())) {
      throw new FunctionalErrorException("A new car cannot already have an ID.");
    }

    // Process category
    processCategoryForCar(car);

    // Process prices
    if (!car.getArticle().getPrices().isEmpty()) {
      processPricesForArticle(car.getArticle().getCategory(), car.getArticle().getPrices());
    }

    return carRepository.save(car);
  }

  @Override
  public Car updateCar(String carId, Car car) throws FunctionalErrorException {
    if (Strings.isBlank(carId)) {
      throw new FunctionalErrorException("Invalid id");
    }

    // Directly call the method instead of using self-reference
    Car existingCar =
        carRepository
            .findById(carId)
            .orElseThrow(() -> new FunctionalErrorException("Car with id " + carId + " not found"));

    // Update basic car properties
    existingCar.setBrand(car.getBrand());
    existingCar.setModel(car.getModel());
    existingCar.setFactoryYear(car.getFactoryYear());

    // Update category if changed
    if (Objects.nonNull(car.getArticle().getCategory().getCategoryId())
        && !Objects.equals(
            car.getArticle().getCategory().getCategoryId(),
            existingCar.getArticle().getCategory().getCategoryId())) {
      existingCar
          .getArticle()
          .setCategory(
              categoryService.getCategoryById(car.getArticle().getCategory().getCategoryId()));
    }

    // Update article properties
    existingCar.getArticle().setName(car.getArticle().getName());
    existingCar.getArticle().setDescription(car.getArticle().getDescription());
    existingCar.getArticle().setCityLocation(car.getArticle().getCityLocation());
    existingCar.getArticle().setDistrictLocation(car.getArticle().getDistrictLocation());
    existingCar.getArticle().setAvailable(car.getArticle().isAvailable());
    existingCar.getArticle().setVisible(car.getArticle().isVisible());

    // Process prices
    if (!car.getArticle().getPrices().isEmpty()) {
      updatePricesForExistingCar(existingCar, car);
    }

    return carRepository.save(existingCar);
  }

  @Override
  @Transactional(readOnly = true)
  public Car getCarById(String carId) {
    if (Strings.isBlank(carId)) {
      throw new FunctionalErrorException("Car ID cannot be null or empty");
    }
    return carRepository
        .findById(carId)
        .orElseThrow(() -> new FunctionalErrorException("Car with id " + carId + " not found"));
  }

  @Override
  @Transactional(readOnly = true)
  public List<Car> getAllCars() {
    return carRepository.findAll();
  }

  @Override
  @Transactional(readOnly = true)
  public Page<Car> searchCars(CarSearchCriteria criteria, Pageable pageable) {
    Specification<Car> spec = CarSpecification.fromCriteria(criteria);
    return carRepository.findAll(spec, pageable);
  }

  /**
   * Process and validate the category for a car.
   *
   * @param car The car entity to process
   */
  private void processCategoryForCar(Car car) {
    if (Objects.isNull(car.getArticle())) {
      throw new FunctionalErrorException("Car must have an article");
    }

    if (Objects.isNull(car.getArticle().getCategory())) {
      throw new FunctionalErrorException("Article must have a category");
    }

    if (Objects.nonNull(car.getArticle().getCategory().getCategoryId())) {
      car.getArticle()
          .setCategory(
              categoryService.getCategoryById(car.getArticle().getCategory().getCategoryId()));
    }
  }

  /**
   * Process and validate prices for an article.
   *
   * @param category The category to validate prices against
   * @param prices The list of prices to process
   */
  private void processPricesForArticle(Category category, List<Price> prices) {
    prices.forEach(
        price -> {
          validatePriceAgainstCategory(category, price);
          processRequiredPriceRelationships(price);
        });
  }

  /**
   * Validate that a price is within the min and max range of a category.
   *
   * @param category The category with price constraints
   * @param price The price to validate
   */
  private void validatePriceAgainstCategory(Category category, Price price) {
    if (category.getMinPrice().compareTo(price.getPrice()) > 0
        || category.getMaxPrice().compareTo(price.getPrice()) < 0) {
      throw new FunctionalErrorException(
          "The price must be between the min price ("
              + category.getMinPrice()
              + ") and max price ("
              + category.getMaxPrice()
              + ") of the category.");
    }
  }

  /**
   * Update prices for an existing car.
   *
   * @param existingCar The existing car to update
   * @param updatedCar The car with updated information
   */
  private void updatePricesForExistingCar(Car existingCar, Car updatedCar) {
    Category category = existingCar.getArticle().getCategory();

    updatedCar
        .getArticle()
        .getPrices()
        .forEach(
            price -> {
              if (Objects.nonNull(price.getPriceId())) {
                updateExistingPrice(existingCar, price, category);
              } else {
                addNewPrice(existingCar, price, category);
              }
            });
  }

  /**
   * Updates an existing price with new values.
   *
   * @param existingCar The car containing the price to update
   * @param updatedPrice The price with updated values
   * @param category The category for price validation
   */
  private void updateExistingPrice(Car existingCar, Price updatedPrice, Category category) {
    existingCar.getArticle().getPrices().stream()
        .filter(p -> p.getPriceId().equals(updatedPrice.getPriceId()))
        .findFirst()
        .ifPresent(
            existingPrice -> {
              validatePriceAgainstCategory(category, updatedPrice);

              // Update basic price properties
              existingPrice.setPrice(updatedPrice.getPrice());
              existingPrice.setCurrency(updatedPrice.getCurrency());
              existingPrice.setDiscountPercent(updatedPrice.getDiscountPercent());

              // Update article option if provided
              updateArticleOptionIfPresent(existingPrice, updatedPrice);

              // Update catalog if provided
              updateCatalogIfPresent(existingPrice, updatedPrice);
            });
  }

  /**
   * Updates the article option of a price if present in the updated price.
   *
   * @param existingPrice The price to update
   * @param updatedPrice The price with updated values
   */
  private void updateArticleOptionIfPresent(Price existingPrice, Price updatedPrice) {
    if (Objects.nonNull(updatedPrice.getArticleOption())
        && Objects.nonNull(updatedPrice.getArticleOption().getOptionId())) {
      existingPrice.setArticleOption(
          articleOptionService.getArticleOptionById(updatedPrice.getArticleOption().getOptionId()));
    }
  }

  /**
   * Updates the catalog of a price if present in the updated price.
   *
   * @param existingPrice The price to update
   * @param updatedPrice The price with updated values
   */
  private void updateCatalogIfPresent(Price existingPrice, Price updatedPrice) {
    if (Objects.nonNull(updatedPrice.getCatalog())
        && Objects.nonNull(updatedPrice.getCatalog().getCatalogId())) {
      existingPrice.setCatalog(
          catalogService.getCatalogById(updatedPrice.getCatalog().getCatalogId()));
    }
  }

  /**
   * Adds a new price to an existing car.
   *
   * @param existingCar The car to add the price to
   * @param newPrice The new price to add
   * @param category The category for price validation
   */
  private void addNewPrice(Car existingCar, Price newPrice, Category category) {
    validatePriceAgainstCategory(category, newPrice);

    // Process required relationships
    processRequiredPriceRelationships(newPrice);

    // Set article and add to car
    newPrice.setArticle(existingCar.getArticle());
    existingCar.getArticle().getPrices().add(newPrice);
  }

  /**
   * Processes and validates required relationships for a price.
   *
   * @param price The price to process
   */
  private void processRequiredPriceRelationships(Price price) {
    // Process article option
    if (Objects.isNull(price.getArticleOption())
        || Objects.isNull(price.getArticleOption().getOptionId())) {
      throw new FunctionalErrorException("Price must have an article option");
    }
    price.setArticleOption(
        articleOptionService.getArticleOptionById(price.getArticleOption().getOptionId()));

    // Process catalog
    if (Objects.isNull(price.getCatalog()) || Objects.isNull(price.getCatalog().getCatalogId())) {
      throw new FunctionalErrorException("Price must have a catalog");
    }
    price.setCatalog(catalogService.getCatalogById(price.getCatalog().getCatalogId()));
  }
}
