package sarl.eazycar.items.domain.service.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.repository.ArticleRepository;
import sarl.eazycar.items.domain.service.IArticleService;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/4/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/4/25
 * @project : EazyCar
 */
@Service
@RequiredArgsConstructor
public class ArticleService implements IArticleService {

  private final ArticleRepository articleRepository;

  @Override
  public Article getArticleById(String articleId) {
    if (Strings.isBlank(articleId)) {
      throw new FunctionalErrorException("Article ID cannot be null or empty");
    }
    return articleRepository
        .findById(articleId)
        .orElseThrow(
            () -> new FunctionalErrorException("Article with id " + articleId + " not found"));
  }

  @Override
  public List<Article> getAllArticles() {
    return articleRepository.findAll();
  }
}
