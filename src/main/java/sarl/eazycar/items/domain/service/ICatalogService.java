package sarl.eazycar.items.domain.service;

import java.util.List;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Catalog;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
public interface ICatalogService {

  Catalog createCatalog(Catalog catalog) throws FunctionalErrorException;

  Catalog getCatalogById(String catalogId) throws FunctionalErrorException;

  Catalog getCatalogByCode(String catalogCode) throws FunctionalErrorException;

  Catalog updateCatalog(String catalogId, Catalog catalog) throws FunctionalErrorException;

  List<Catalog> getAllCatalogs();
}
