package sarl.eazycar.items.domain.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Cart;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.enums.CartStatus;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.repository.CartRepository;
import sarl.eazycar.items.domain.repository.OrderRepository;
import sarl.eazycar.items.domain.service.IOrderService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Implémentation du service Order
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class OrderService implements IOrderService {

    private final OrderRepository orderRepository;
    private final CartRepository cartRepository;

    @Override
    public Order createOrderFromCart(String cartId, String accountId) throws FunctionalErrorException {
        if (Strings.isBlank(cartId)) {
            throw new FunctionalErrorException("Cart ID cannot be null or empty");
        }
        if (Strings.isBlank(accountId)) {
            throw new FunctionalErrorException("Account ID cannot be null or empty");
        }

        Cart cart = cartRepository.findById(cartId)
                .orElseThrow(() -> new FunctionalErrorException("Cart not found: " + cartId));

        if (!cart.getAccountId().equals(accountId)) {
            throw new FunctionalErrorException("Cart does not belong to the specified account");
        }

        if (cart.getStatus() != CartStatus.ACTIVE) {
            throw new FunctionalErrorException("Cart is not active");
        }

        if (cart.getOrders().isEmpty()) {
            throw new FunctionalErrorException("Cart is empty");
        }

        // Convertir les ordres du panier en commandes confirmées
        cart.getOrders().forEach(order -> {
            order.setStatus(OrderStatus.PENDING);
            order.setConfirmationDate(LocalDateTime.now());
            orderRepository.save(order);
        });

        // Marquer le panier comme traité
        cart.setStatus(CartStatus.CHECKED_OUT);
        cartRepository.save(cart);

        // Retourner la première commande (ou on pourrait retourner une liste)
        return cart.getOrders().get(0);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Order> getOrdersByAccountId(String accountId) {
        if (Strings.isBlank(accountId)) {
            throw new FunctionalErrorException("Account ID cannot be null or empty");
        }

        return orderRepository.findByAccountId(accountId);
    }

    @Override
    @Transactional(readOnly = true)
    public Order getOrderById(String orderId) throws FunctionalErrorException {
        if (Strings.isBlank(orderId)) {
            throw new FunctionalErrorException("Order ID cannot be null or empty");
        }

        return orderRepository.findById(orderId)
                .orElseThrow(() -> new FunctionalErrorException("Order not found: " + orderId));
    }

    @Override
    public Order updateOrderStatus(String orderId, OrderStatus newStatus) throws FunctionalErrorException {
        if (Strings.isBlank(orderId)) {
            throw new FunctionalErrorException("Order ID cannot be null or empty");
        }
        if (Objects.isNull(newStatus)) {
            throw new FunctionalErrorException("New status cannot be null");
        }

        Order order = getOrderById(orderId);
        
        // Valider la transition de statut
        validateStatusTransition(order.getStatus(), newStatus);
        
        order.setStatus(newStatus);
        
        // Mettre à jour la date de confirmation si nécessaire
        if (newStatus == OrderStatus.CONFIRMED && Objects.isNull(order.getConfirmationDate())) {
            order.setConfirmationDate(LocalDateTime.now());
        }

        return orderRepository.save(order);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Order> getOrdersByStatus(OrderStatus status) {
        if (Objects.isNull(status)) {
            throw new FunctionalErrorException("Status cannot be null");
        }

        return orderRepository.findByStatus(status);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Order> getOrdersByCartId(String cartId) {
        if (Strings.isBlank(cartId)) {
            throw new FunctionalErrorException("Cart ID cannot be null or empty");
        }

        return orderRepository.findByCartCartId(cartId);
    }

    private void validateStatusTransition(OrderStatus currentStatus, OrderStatus newStatus) {
        // Règles de transition de statut
        switch (currentStatus) {
            case IN_CART:
                if (newStatus != OrderStatus.PENDING && newStatus != OrderStatus.CANCELLED) {
                    throw new FunctionalErrorException(
                        "Invalid status transition from IN_CART to " + newStatus);
                }
                break;
            case PENDING:
                if (newStatus != OrderStatus.CONFIRMED && newStatus != OrderStatus.CANCELLED) {
                    throw new FunctionalErrorException(
                        "Invalid status transition from PENDING to " + newStatus);
                }
                break;
            case CONFIRMED:
                if (newStatus != OrderStatus.CANCELLED) {
                    throw new FunctionalErrorException(
                        "Invalid status transition from CONFIRMED to " + newStatus);
                }
                break;
            case CANCELLED:
                throw new FunctionalErrorException(
                    "Cannot change status of a cancelled order");
            default:
                throw new FunctionalErrorException("Unknown order status: " + currentStatus);
        }
    }
}
