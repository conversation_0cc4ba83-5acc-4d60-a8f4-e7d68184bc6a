package sarl.eazycar.items.domain.service;

import java.util.List;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Category;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
public interface ICategoryService {

  Category createCategory(Category category) throws FunctionalErrorException;

  Category getCategoryByType(String type) throws FunctionalErrorException;

  Category getCategoryById(String categoryId) throws FunctionalErrorException;

  Category updateCategory(String categoryId, Category category) throws FunctionalErrorException;

  List<Category> getAllCategories();
}
