package sarl.eazycar.items.domain.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Invoice;
import sarl.eazycar.items.domain.entity.Payment;
import sarl.eazycar.items.domain.entity.enums.InvoiceStatus;
import sarl.eazycar.items.domain.entity.enums.PaymentMethod;
import sarl.eazycar.items.domain.entity.enums.PaymentStatus;
import sarl.eazycar.items.domain.repository.InvoiceRepository;
import sarl.eazycar.items.domain.repository.PaymentRepository;
import sarl.eazycar.items.domain.service.IPaymentService;

/** Implémentation du service Payment */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class PaymentService implements IPaymentService {

  private final PaymentRepository paymentRepository;
  private final InvoiceRepository invoiceRepository;

  @Override
  public Payment recordPayment(
      String invoiceId, BigDecimal amount, PaymentMethod paymentMethod, String transactionId)
      throws FunctionalErrorException {
    if (Strings.isBlank(invoiceId)) {
      throw new FunctionalErrorException("Invoice ID cannot be null or empty");
    }
    if (Objects.isNull(amount) || amount.compareTo(BigDecimal.ZERO) <= 0) {
      throw new FunctionalErrorException("Amount must be greater than zero");
    }
    if (Objects.isNull(paymentMethod)) {
      throw new FunctionalErrorException("Payment method cannot be null");
    }
    if (Strings.isBlank(transactionId)) {
      throw new FunctionalErrorException("Transaction ID cannot be null or empty");
    }

    // Vérifier que la facture existe
    Invoice invoice =
        invoiceRepository
            .findById(invoiceId)
            .orElseThrow(() -> new FunctionalErrorException("Invoice not found: " + invoiceId));

    // Vérifier que la facture peut recevoir des paiements
    if (invoice.getStatus() == InvoiceStatus.PAID) {
      throw new FunctionalErrorException("Invoice is already paid");
    }

    // Vérifier l'unicité du transaction ID
    paymentRepository
        .findByTransactionId(transactionId)
        .ifPresent(
            existingPayment -> {
              throw new FunctionalErrorException(
                  "Payment with transaction ID already exists: " + transactionId);
            });

    Payment payment =
        Payment.builder()
            .paymentDate(LocalDateTime.now())
            .amount(amount)
            .paymentMethod(paymentMethod)
            .transactionId(transactionId)
            .status(PaymentStatus.SUCCESSFUL)
            .invoice(invoice)
            .build();

    Payment savedPayment = paymentRepository.save(payment);

    // Vérifier si la facture est maintenant entièrement payée
    checkAndUpdateInvoiceStatus(invoice);

    return savedPayment;
  }

  @Override
  @Transactional(readOnly = true)
  public List<Payment> getPaymentsByInvoiceId(String invoiceId) {
    if (Strings.isBlank(invoiceId)) {
      throw new FunctionalErrorException("Invoice ID cannot be null or empty");
    }

    return paymentRepository.findPaymentsByInvoiceInvoiceId(invoiceId);
  }

  @Override
  @Transactional(readOnly = true)
  public Payment getPaymentById(String paymentId) throws FunctionalErrorException {
    if (Strings.isBlank(paymentId)) {
      throw new FunctionalErrorException("Payment ID cannot be null or empty");
    }

    return paymentRepository
        .findById(paymentId)
        .orElseThrow(() -> new FunctionalErrorException("Payment not found: " + paymentId));
  }

  @Override
  public Payment updatePaymentStatus(String paymentId, PaymentStatus newStatus)
      throws FunctionalErrorException {
    if (Strings.isBlank(paymentId)) {
      throw new FunctionalErrorException("Payment ID cannot be null or empty");
    }
    if (Objects.isNull(newStatus)) {
      throw new FunctionalErrorException("New status cannot be null");
    }

    Payment payment = getPaymentById(paymentId);

    // Valider la transition de statut
    validateStatusTransition(payment.getStatus(), newStatus);

    payment.setStatus(newStatus);
    Payment savedPayment = paymentRepository.save(payment);

    // Mettre à jour le statut de la facture si nécessaire
    checkAndUpdateInvoiceStatus(payment.getInvoice());

    return savedPayment;
  }

  @Override
  @Transactional(readOnly = true)
  public List<Payment> getPaymentsByStatus(PaymentStatus status) {
    if (Objects.isNull(status)) {
      throw new FunctionalErrorException("Status cannot be null");
    }

    return paymentRepository.findByStatus(status);
  }

  @Override
  @Transactional(readOnly = true)
  public Payment getPaymentByTransactionId(String transactionId) throws FunctionalErrorException {
    if (Strings.isBlank(transactionId)) {
      throw new FunctionalErrorException("Transaction ID cannot be null or empty");
    }

    return paymentRepository
        .findByTransactionId(transactionId)
        .orElseThrow(
            () ->
                new FunctionalErrorException(
                    "Payment not found for transaction: " + transactionId));
  }

  private void validateStatusTransition(PaymentStatus currentStatus, PaymentStatus newStatus) {
    // Règles de transition de statut pour les paiements
    switch (currentStatus) {
      case PENDING:
        if (newStatus != PaymentStatus.SUCCESSFUL && newStatus != PaymentStatus.FAILED) {
          throw new FunctionalErrorException(
              "Invalid status transition from PENDING to " + newStatus);
        }
        break;
      case SUCCESSFUL:
        throw new FunctionalErrorException("Cannot change status of a successful payment");
      case FAILED:
        if (newStatus != PaymentStatus.PENDING) {
          throw new FunctionalErrorException(
              "Invalid status transition from FAILED to " + newStatus);
        }
        break;
      default:
        throw new FunctionalErrorException("Unknown payment status: " + currentStatus);
    }
  }

  private void checkAndUpdateInvoiceStatus(Invoice invoice) {
    // Calculer le total des paiements réussis
    BigDecimal totalPaid =
        invoice.getPayments().stream()
            .filter(payment -> payment.getStatus() == PaymentStatus.SUCCESSFUL)
            .map(Payment::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

    // Calculer le montant total de la facture (basé sur la commande)
    BigDecimal invoiceTotal = calculateInvoiceTotal(invoice);

    // Mettre à jour le statut de la facture si entièrement payée
    if (totalPaid.compareTo(invoiceTotal) >= 0 && invoice.getStatus() != InvoiceStatus.PAID) {
      invoice.setStatus(InvoiceStatus.PAID);
      invoiceRepository.save(invoice);
    }
  }

  private BigDecimal calculateInvoiceTotal(Invoice invoice) {
    // Calculer le montant total basé sur la commande
    if (Objects.isNull(invoice.getOrder())
        || Objects.isNull(invoice.getOrder().getQuantity())
        || Objects.isNull(invoice.getOrder().getUnitPrice())) {
      return BigDecimal.ZERO;
    }

    return invoice
        .getOrder()
        .getUnitPrice()
        .multiply(BigDecimal.valueOf(invoice.getOrder().getQuantity()));
  }
}
