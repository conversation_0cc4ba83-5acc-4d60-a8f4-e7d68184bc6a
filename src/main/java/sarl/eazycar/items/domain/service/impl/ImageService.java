package sarl.eazycar.items.domain.service.impl;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Image;
import sarl.eazycar.items.domain.repository.ImageRepository;
import sarl.eazycar.items.domain.service.IArticleService;
import sarl.eazycar.items.domain.service.IImageService;
import sarl.eazycar.items.infrastructure.config.ApplicationProperties;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/4/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/4/25
 * @project : EazyCar
 */
@Service
@RequiredArgsConstructor
@Transactional
public class ImageService implements IImageService {
  private final ImageRepository imageRepository;
  private final IArticleService articleService;
  private final ApplicationProperties applicationProperties;

  @Override
  public List<Image> createImage(List<Image> images, String articleId) {
    var count = imageRepository.countByArticleArticleId(articleId);
    if (count >= applicationProperties.getMediaSize().getImageLength()) {
      throw new FunctionalErrorException(
          "Images must be less than " + applicationProperties.getMediaSize().getImageLength());
    }
    Article article = articleService.getArticleById(articleId);
    images.forEach(
        image -> {
          image.setArticle(article);
        });
    return imageRepository.saveAll(images);
  }
}
