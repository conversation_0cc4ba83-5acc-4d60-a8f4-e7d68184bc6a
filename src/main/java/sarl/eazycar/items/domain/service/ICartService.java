package sarl.eazycar.items.domain.service;

import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Cart;
import sarl.eazycar.items.domain.entity.Order;

/**
 * Interface pour le service Cart
 */
public interface ICartService {
    
    /**
     * Obtenir ou créer un panier pour un utilisateur
     */
    Cart getOrCreateActiveCart(String accountId);
    
    /**
     * Ajouter une commande au panier
     */
    Cart addOrderToCart(String accountId, Order order) throws FunctionalErrorException;
    
    /**
     * Obtenir le contenu du panier d'un utilisateur
     */
    Cart getCartWithOrders(String accountId) throws FunctionalErrorException;
    
    /**
     * Mettre à jour la quantité d'une commande
     */
    Cart updateOrderQuantity(String accountId, String orderId, Integer newQuantity) throws FunctionalErrorException;
    
    /**
     * Retirer une commande du panier
     */
    Cart removeOrderFromCart(String accountId, String orderId) throws FunctionalErrorException;
    
    /**
     * Vider le panier
     */
    Cart clearCart(String accountId) throws FunctionalErrorException;
    
    /**
     * Obtenir un panier par son ID
     */
    Cart getCartById(String cartId) throws FunctionalErrorException;
    
    /**
     * Calculer le montant total du panier
     */
    Cart calculateCartTotal(String cartId) throws FunctionalErrorException;
}
