package sarl.eazycar.items.domain.service;

import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Payment;
import sarl.eazycar.items.domain.entity.enums.PaymentMethod;
import sarl.eazycar.items.domain.entity.enums.PaymentStatus;

import java.math.BigDecimal;
import java.util.List;

/**
 * Interface pour le service Payment
 */
public interface IPaymentService {
    
    /**
     * Enregistrer un paiement pour une facture
     */
    Payment recordPayment(String invoiceId, BigDecimal amount, PaymentMethod paymentMethod, 
                         String transactionId) throws FunctionalErrorException;
    
    /**
     * Obtenir la liste des paiements pour une facture
     */
    List<Payment> getPaymentsByInvoiceId(String invoiceId);
    
    /**
     * Obtenir un paiement par son ID
     */
    Payment getPaymentById(String paymentId) throws FunctionalErrorException;
    
    /**
     * Mettre à jour le statut d'un paiement
     */
    Payment updatePaymentStatus(String paymentId, PaymentStatus newStatus) throws FunctionalErrorException;
    
    /**
     * Obtenir les paiements par statut
     */
    List<Payment> getPaymentsByStatus(PaymentStatus status);
    
    /**
     * Obtenir un paiement par transaction ID
     */
    Payment getPaymentByTransactionId(String transactionId) throws FunctionalErrorException;
}
