package sarl.eazycar.items.domain.service;

import java.util.List;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Article;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/4/25
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON> KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/4/25
 * @project : EazyCar
 */
public interface IArticleService {

  /**
   * Get an article by its ID
   *
   * @param articleId the ID of the article to retrieve
   * @return the article
   * @throws FunctionalErrorException if the article is not found
   */
  Article getArticleById(String articleId) throws FunctionalErrorException;

  /**
   * Get all articles
   *
   * @return a list of all articles
   */
  List<Article> getAllArticles();
}
