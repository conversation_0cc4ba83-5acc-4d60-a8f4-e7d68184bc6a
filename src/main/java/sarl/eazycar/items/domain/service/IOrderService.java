package sarl.eazycar.items.domain.service;

import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;

import java.util.List;

/**
 * Interface pour le service Order
 */
public interface IOrderService {
    
    /**
     * Créer une commande à partir d'un panier
     */
    Order createOrderFromCart(String cartId, String accountId) throws FunctionalErrorException;
    
    /**
     * Obtenir la liste des commandes d'un utilisateur
     */
    List<Order> getOrdersByAccountId(String accountId);
    
    /**
     * Obtenir les détails d'une commande spécifique
     */
    Order getOrderById(String orderId) throws FunctionalErrorException;
    
    /**
     * Mettre à jour le statut d'une commande
     */
    Order updateOrderStatus(String orderId, OrderStatus newStatus) throws FunctionalErrorException;
    
    /**
     * Obtenir les commandes par statut
     */
    List<Order> getOrdersByStatus(OrderStatus status);
    
    /**
     * Obtenir les commandes d'un panier
     */
    List<Order> getOrdersByCartId(String cartId);
}
