package sarl.eazycar.items.domain.service.impl;

import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.ArticleOption;
import sarl.eazycar.items.domain.repository.ArticleOptionRepository;
import sarl.eazycar.items.domain.service.IArticleOptionService;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@Service
@RequiredArgsConstructor
public class ArticleOptionService implements IArticleOptionService {

  private final ArticleOptionRepository articleOptionRepository;

  @Override
  public ArticleOption createArticleOption(ArticleOption articleOption) {
    if (Objects.nonNull(articleOption.getOptionId())) {
      throw new FunctionalErrorException("A new articleOption cannot already have an ID.");
    }
    articleOptionRepository
        .findArticleOptionByOptionNameIgnoreCase(articleOption.getOptionName())
        .ifPresent(
            articleOption1 -> {
              throw new FunctionalErrorException(
                  String.format(
                      "The articleOption name %s already exists.", articleOption.getOptionName()));
            });

    return articleOptionRepository.save(articleOption);
  }

  @Override
  public ArticleOption getArticleOptionById(String optionId) {
    return articleOptionRepository
        .findById(optionId)
        .orElseThrow(
            () -> new FunctionalErrorException("ArticleOption with id " + optionId + " not found"));
  }

  @Override
  public ArticleOption getArticleOptionByName(String optionName) {
    return articleOptionRepository
        .findArticleOptionByOptionNameIgnoreCase(optionName)
        .orElseThrow(
            () ->
                new FunctionalErrorException(
                    "ArticleOption with name " + optionName + " not found"));
  }

  @Override
  public ArticleOption updateArticleOption(String articleOptionId, ArticleOption articleOption) {
    if (Strings.isBlank(articleOptionId)) {
      throw new FunctionalErrorException("Invalid id");
    }
    ArticleOption existingArticleOption = getArticleOptionById(articleOptionId);

    if (!existingArticleOption.getOptionName().equalsIgnoreCase(articleOption.getOptionName())) {
      articleOptionRepository
          .findArticleOptionByOptionNameIgnoreCase(articleOption.getOptionName())
          .ifPresent(
              articleOption1 -> {
                throw new FunctionalErrorException(
                    String.format(
                        "The articleOption name %s already exists.",
                        articleOption.getOptionName()));
              });
    }
    existingArticleOption.setOptionName(articleOption.getOptionName());
    existingArticleOption.setOptionDescription(articleOption.getOptionDescription());
    return articleOptionRepository.save(existingArticleOption);
  }

  @Override
  public List<ArticleOption> getAllArticleOptions() {
    return articleOptionRepository.findAll();
  }
}
