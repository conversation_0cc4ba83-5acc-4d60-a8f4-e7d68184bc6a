package sarl.eazycar.items.domain.service;

import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import sarl.eazycar.items.application.dto.criteria.CarSearchCriteria;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Car;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
public interface ICarService {

  Car createCar(Car car) throws FunctionalErrorException;

  Car updateCar(String carId, Car car) throws FunctionalErrorException;

  Car getCarById(String carId) throws FunctionalErrorException;

  List<Car> getAllCars();

  Page<Car> searchCars(CarSearchCriteria criteria, Pageable pageable);
}
