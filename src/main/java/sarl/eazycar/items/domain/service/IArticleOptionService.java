package sarl.eazycar.items.domain.service;

import java.util.List;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.ArticleOption;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
public interface IArticleOptionService {

  ArticleOption createArticleOption(ArticleOption articleOption) throws FunctionalErrorException;

  ArticleOption getArticleOptionById(String articleOptionId) throws FunctionalErrorException;

  ArticleOption getArticleOptionByName(String articleOptionName) throws FunctionalErrorException;

  ArticleOption updateArticleOption(String articleOptionId, ArticleOption articleOption)
      throws FunctionalErrorException;

  List<ArticleOption> getAllArticleOptions();
}
