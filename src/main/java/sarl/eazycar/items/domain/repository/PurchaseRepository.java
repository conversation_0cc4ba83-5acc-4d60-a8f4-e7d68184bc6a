package sarl.eazycar.items.domain.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface PurchaseRepository extends JpaRepository<Purchase, String> {
    
    List<Purchase> findByAccountId(String accountId);
    
    List<Purchase> findByStatus(OrderStatus status);

    @Query("SELECT p FROM Purchase p WHERE p.estimatedDeliveryDate BETWEEN :startDate AND :endDate")
    List<Purchase> findByEstimatedDeliveryDateBetween(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    @Query("SELECT p FROM Purchase p WHERE p.accountId = :accountId AND p.status = :status")
    List<Purchase> findByAccountIdAndStatus(@Param("accountId") String accountId, @Param("status") OrderStatus status);
}
