package sarl.eazycar.items.domain.repository.specification;

import static sarl.eazycar.items.domain.repository.specification.CarSpecification.ATTRIBUTE_ARTICLE;
import static sarl.eazycar.items.domain.repository.specification.CarSpecification.ATTRIBUTE_CATALOG;
import static sarl.eazycar.items.domain.repository.specification.CarSpecification.ATTRIBUTE_CATEGORY;
import static sarl.eazycar.items.domain.repository.specification.CarSpecification.ATTRIBUTE_PRICES;

import jakarta.persistence.criteria.From;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Car;
import sarl.eazycar.items.domain.entity.Catalog;
import sarl.eazycar.items.domain.entity.Category;
import sarl.eazycar.items.domain.entity.Price;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 6/5/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 6/5/25
 * @project : EazyCar
 */
public class JoinManager {
  private final Root<Car> root;
  private Join<Car, Article> articleJoin;
  private Join<Article, Category> categoryJoin;
  private Join<Article, Price> priceJoin;
  private Join<Price, Catalog> catalogJoin;

  public JoinManager(Root<Car> root) {
    this.root = root;
  }

  public Join<Car, Article> getArticleJoin() {
    if (articleJoin == null) articleJoin = findOrCreateJoin(root, ATTRIBUTE_ARTICLE);
    return articleJoin;
  }

  public Join<Article, Category> getCategoryJoin() {
    if (categoryJoin == null) categoryJoin = findOrCreateJoin(getArticleJoin(), ATTRIBUTE_CATEGORY);
    return categoryJoin;
  }

  public Join<Article, Price> getPriceJoin() {
    if (priceJoin == null) priceJoin = findOrCreateJoin(getArticleJoin(), ATTRIBUTE_PRICES);
    return priceJoin;
  }

  public Join<Price, Catalog> getCatalogJoin(Join<Article, Price> currentPriceJoin) {
    if (catalogJoin == null || !catalogJoin.getParent().equals(currentPriceJoin)) {
      catalogJoin = findOrCreateJoin(currentPriceJoin, ATTRIBUTE_CATALOG);
    }
    return catalogJoin;
  }

  @SuppressWarnings("unchecked")
  private <X, Y> Join<X, Y> findOrCreateJoin(From<?, X> from, String attributeName) {
    // Recherche basique d'un join existant.
    for (Join<?, ?> j : from.getJoins()) {
      if (j.getAttribute().getName().equals(attributeName)
          && j.getJoinType().equals(JoinType.LEFT)) {
        return (Join<X, Y>) j;
      }
    }
    return (Join<X, Y>) from.join(attributeName, JoinType.LEFT);
  }
}
