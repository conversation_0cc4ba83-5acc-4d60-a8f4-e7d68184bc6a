package sarl.eazycar.items.domain.repository;

import java.util.Optional;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import sarl.eazycar.items.domain.entity.ArticleOption;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@Repository
public interface ArticleOptionRepository extends JpaRepository<ArticleOption, String> {

  /**
   * update the description of an {@link ArticleOption} identified by its name.
   *
   * @param optionName the name of the option to be update
   * @param optionDescription the new description to be set
   */
  @Modifying(clearAutomatically = true)
  @Query(
      "update ArticleOption a set a.optionDescription = :optionDescription where a.optionName = :optionName")
  void updateArticleOptionByOptionName(
      @Param("optionName") String optionName, @Param("optionDescription") String optionDescription);

  /**
   * update an {@link ArticleOption} identified by its id.
   *
   * @param optionId the id of the option to be update
   * @param optionName the new name to be set
   * @param optionDescription the new description to be set
   */
  @Modifying(clearAutomatically = true)
  @Query(
      "update ArticleOption a set a.optionName = :optionName, a.optionDescription = :optionDescription where a.optionId = :optionId")
  void updateArticleOptionByOptionId(
      @Param("optionId") String optionId,
      @Param("optionName") String optionName,
      @Param("optionDescription") String optionDescription);

  @EntityGraph(value = "ArticleOption.withPrices")
  Optional<ArticleOption> findArticleOptionByOptionNameIgnoreCase(String optionName);
}
