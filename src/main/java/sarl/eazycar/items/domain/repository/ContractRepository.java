package sarl.eazycar.items.domain.repository;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import sarl.eazycar.items.domain.entity.Contract;
import sarl.eazycar.items.domain.entity.enums.ContractStatus;

@Repository
public interface ContractRepository extends JpaRepository<Contract, String> {

  Optional<Contract> findByOrderOrderId(String orderId);

  List<Contract> findByStatus(ContractStatus status);

  @Query("SELECT c FROM Contract c WHERE c.createdDate BETWEEN :startDate AND :endDate")
  List<Contract> findByCreatedDateBetween(
      @Param("startDate") Instant startDate, @Param("endDate") Instant endDate);

  @Query("SELECT c FROM Contract c WHERE c.signatureDate BETWEEN :startDate AND :endDate")
  List<Contract> findBySignatureDateBetween(
      @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
}
