package sarl.eazycar.items.domain.repository.specification;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import sarl.eazycar.items.application.dto.criteria.CarSearchCriteria;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.ArticleOption;
import sarl.eazycar.items.domain.entity.Car;
import sarl.eazycar.items.domain.entity.Catalog;
import sarl.eazycar.items.domain.entity.Price;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 6/5/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 6/5/25
 * @project : EazyCar
 */
@Slf4j
public class CarSpecification {

  public static final String ATTRIBUTE_BRAND = "brand";
  public static final String ATTRIBUTE_MODEL = "model";
  public static final String ATTRIBUTE_FACTORY_YEAR = "factoryYear";
  public static final String ATTRIBUTE_ARTICLE = "article";
  public static final String ATTRIBUTE_CITY_LOCATION = "cityLocation";
  public static final String ATTRIBUTE_DISTRICT_LOCATION = "districtLocation";
  public static final String ATTRIBUTE_AVAILABLE = "available";
  public static final String ATTRIBUTE_CATEGORY = "category";
  public static final String ATTRIBUTE_CATEGORY_TYPE = "type";
  public static final String ATTRIBUTE_PRICES = "prices";
  public static final String ATTRIBUTE_CATALOG = "catalog";
  public static final String ATTRIBUTE_CATALOG_CODE = "code";
  public static final String ATTRIBUTE_PRICE_AMOUNT = "price";
  public static final String ATTRIBUTE_CURRENCY = "currency";
  public static final String ATTRIBUTE_ARTICLE_OPTION = "articleOption";
  public static final String ATTRIBUTE_OPTION_NAME = "optionName";

  private CarSpecification() {}

  public static Specification<Car> fromCriteria(final CarSearchCriteria criteria) {
    return (Root<Car> root, CriteriaQuery<?> query, CriteriaBuilder cb) -> {
      List<Predicate> predicates = new ArrayList<>();
      JoinManager joinManager = new JoinManager(root);

      // Filtres sur Car
      addStringLikePredicate(predicates, cb, root, ATTRIBUTE_BRAND, criteria.getCarBrand());
      addStringLikePredicate(predicates, cb, root, ATTRIBUTE_MODEL, criteria.getCarModel());
      addFactoryYearPredicates(
          predicates, cb, root, criteria.getMinFactoryYear(), criteria.getMaxFactoryYear());

      // Filtres sur Article (via Join)
      addArticlePredicates(predicates, cb, criteria, joinManager);

      // Filtre sur Category (via Article)
      addCategoryPredicate(predicates, cb, criteria, joinManager);

      // Filtres sur Price et Catalog (via Article)
      boolean needsDistinct = addPriceAndCatalogPredicates(predicates, cb, criteria, joinManager);

      // Filtre sur ArticleOption (via Article, Price)
      boolean needsDistinctForOptions =
          addArticleOptionPredicates(predicates, cb, query, root, criteria);

      if (needsDistinct || needsDistinctForOptions) {
        query.distinct(true);
      }

      return cb.and(predicates.toArray(new Predicate[0]));
    };
  }

  private static <T> void addStringLikePredicate(
      List<Predicate> predicates,
      CriteriaBuilder cb,
      Path<T> path,
      String attribute,
      String value) {
    if (StringUtils.hasText(value)) {
      predicates.add(cb.like(cb.lower(path.get(attribute)), "%" + value.toLowerCase() + "%"));
    }
  }

  private static void addFactoryYearPredicates(
      List<Predicate> predicates,
      CriteriaBuilder cb,
      Root<Car> root,
      Integer minYear,
      Integer maxYear) {
    BiFunction<Expression<Integer>, Integer, Predicate> lessThanOrEqual = cb::lessThanOrEqualTo;
    BiFunction<Expression<Integer>, Integer, Predicate> greaterThanOrEqual =
        cb::greaterThanOrEqualTo;

    Function<Root<Car>, Expression<Integer>> yearExpressionFn = r -> r.get(ATTRIBUTE_FACTORY_YEAR);

    if (minYear != null) {
      Optional.ofNullable(yearExpressionFn.apply(root))
          .ifPresent(expr -> predicates.add(greaterThanOrEqual.apply(expr, minYear)));
    }
    if (maxYear != null) {
      Optional.ofNullable(yearExpressionFn.apply(root))
          .ifPresent(expr -> predicates.add(lessThanOrEqual.apply(expr, maxYear)));
    }
  }

  private static void addArticlePredicates(
      List<Predicate> predicates,
      CriteriaBuilder cb,
      CarSearchCriteria criteria,
      JoinManager joinManager) {
    if (StringUtils.hasText(criteria.getLocationCity())) {
      predicates.add(
          cb.equal(
              cb.lower(joinManager.getArticleJoin().get(ATTRIBUTE_CITY_LOCATION)),
              criteria.getLocationCity().toLowerCase()));
    }
    if (StringUtils.hasText(criteria.getLocationDistrict())) {
      predicates.add(
          cb.equal(
              cb.lower(joinManager.getArticleJoin().get(ATTRIBUTE_DISTRICT_LOCATION)),
              criteria.getLocationDistrict().toLowerCase()));
    }
    if (criteria.isAvailableOnly()) {
      predicates.add(cb.isTrue(joinManager.getArticleJoin().get(ATTRIBUTE_AVAILABLE)));
    }
  }

  private static void addCategoryPredicate(
      List<Predicate> predicates,
      CriteriaBuilder cb,
      CarSearchCriteria criteria,
      JoinManager joinManager) {
    if (StringUtils.hasText(criteria.getCategoryType())) {
      predicates.add(
          cb.equal(
              cb.lower(joinManager.getCategoryJoin().get(ATTRIBUTE_CATEGORY_TYPE)),
              criteria.getCategoryType().toLowerCase()));
    }
  }

  private static boolean addPriceAndCatalogPredicates(
      List<Predicate> predicates,
      CriteriaBuilder cb,
      CarSearchCriteria criteria,
      JoinManager joinManager) {
    boolean hasPriceCriteria =
        StringUtils.hasText(criteria.getCatalogCode())
            || criteria.getMinPrice() != null
            || criteria.getMaxPrice() != null
            || StringUtils.hasText(criteria.getCurrency());
    if (!hasPriceCriteria) {
      return false;
    }

    Join<Article, Price> priceJoin = joinManager.getPriceJoin();
    Join<Price, Catalog> catalogJoin = joinManager.getCatalogJoin(priceJoin);

    if (StringUtils.hasText(criteria.getCatalogCode())) {
      predicates.add(cb.equal(catalogJoin.get(ATTRIBUTE_CATALOG_CODE), criteria.getCatalogCode()));
    }
    if (criteria.getMinPrice() != null) {
      predicates.add(
          cb.greaterThanOrEqualTo(priceJoin.get(ATTRIBUTE_PRICE_AMOUNT), criteria.getMinPrice()));
    }
    if (criteria.getMaxPrice() != null) {
      predicates.add(
          cb.lessThanOrEqualTo(priceJoin.get(ATTRIBUTE_PRICE_AMOUNT), criteria.getMaxPrice()));
    }
    if (StringUtils.hasText(criteria.getCurrency())) {
      predicates.add(
          cb.equal(
              cb.upper(priceJoin.get(ATTRIBUTE_CURRENCY)), criteria.getCurrency().toUpperCase()));
    }
    return true;
  }

  private static boolean addArticleOptionPredicates(
      List<Predicate> predicates,
      CriteriaBuilder cb,
      CriteriaQuery<?> query,
      Root<Car> carRoot,
      CarSearchCriteria criteria) {
    if (CollectionUtils.isEmpty(criteria.getArticleOptions())) {
      return false;
    }

    // Pour que la voiture ait TOUTES les options spécifiées (ET logique).
    for (String optionName : criteria.getArticleOptions()) {
      Subquery<Long> optionExistsSubquery = query.subquery(Long.class);
      Root<Car> subCarRoot =
          optionExistsSubquery.correlate(carRoot); // Corréler à la Car de la requête principale
      Join<Car, Article> subArticleJoin = subCarRoot.join(ATTRIBUTE_ARTICLE);
      Join<Article, Price> subPriceJoin = subArticleJoin.join(ATTRIBUTE_PRICES);
      Join<Price, ArticleOption> subOptionJoin = subPriceJoin.join(ATTRIBUTE_ARTICLE_OPTION);

      optionExistsSubquery
          .select(cb.literal(1L))
          .where(
              cb.equal(
                  cb.lower(subOptionJoin.get(ATTRIBUTE_OPTION_NAME)), optionName.toLowerCase()));
      predicates.add(cb.exists(optionExistsSubquery));
    }
    return true;
  }
}
