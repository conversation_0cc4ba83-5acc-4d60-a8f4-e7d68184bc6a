package sarl.eazycar.items.domain.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import sarl.eazycar.items.domain.entity.Price;

import java.util.List;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */

@Repository
public interface PriceRepository extends JpaRepository<Price, String> {

	@Query("select p from Price p where p.articleOption.optionName = :optionName and p.article.category.type = :type and p.catalog.code = :code")
	List<Price> findByArticleOptionAndArticleCategoryAndCatalog(String optionName, String type, String code);

	@Query("select p from Price p where p.articleOption.optionName = :optionName and p.catalog.code = :code")
	List<Price> findByArticleOptionAndCatalog(String optionName, String code);
}
