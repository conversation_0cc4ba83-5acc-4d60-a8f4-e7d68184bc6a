package sarl.eazycar.items.domain.repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import sarl.eazycar.items.domain.entity.Invoice;
import sarl.eazycar.items.domain.entity.enums.InvoiceStatus;

@Repository
public interface InvoiceRepository extends JpaRepository<Invoice, String> {

  Optional<Invoice> findByOrderOrderId(String orderId);

  List<Invoice> findByStatus(InvoiceStatus status);

  @Query("SELECT i FROM Invoice i WHERE i.issueDate BETWEEN :startDate AND :endDate")
  List<Invoice> findByIssueDateBetween(
      @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

  @Query("SELECT i FROM Invoice i WHERE i.dueDate BETWEEN :startDate AND :endDate")
  List<Invoice> findByDueDateBetween(
      @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

  @Query(
      "SELECT i FROM Invoice i WHERE i.dueDate < :currentDate AND i.status != sarl.eazycar.items.domain.entity.enums.InvoiceStatus.PAID")
  List<Invoice> findOverdueInvoices(@Param("currentDate") LocalDate currentDate);
}
