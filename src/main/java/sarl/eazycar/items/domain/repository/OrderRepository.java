package sarl.eazycar.items.domain.repository;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;

@Repository
public interface OrderRepository extends JpaRepository<Order, String> {

  List<Order> findByAccountId(String accountId);

  List<Order> findByCartCartId(String cartId);

  List<Order> findByStatus(OrderStatus status);

  @Query("SELECT o FROM Order o WHERE o.accountId = :accountId AND o.status = :status")
  List<Order> findByAccountIdAndStatus(
      @Param("accountId") String accountId, @Param("status") OrderStatus status);
}
