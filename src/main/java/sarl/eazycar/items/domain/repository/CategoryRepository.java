package sarl.eazycar.items.domain.repository;

import java.math.BigDecimal;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import sarl.eazycar.items.domain.entity.Category;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@Repository
public interface CategoryRepository extends JpaRepository<Category, String> {

  /**
   * Updates the label, description, minPrice and maxPrice of a {@link Category} identified by its
   * type.
   *
   * @param label the label of the Category to be updated
   * @param type the new type to be set
   * @param description the new description to be set
   * @param minPrice the new minPrice to be set
   * @param maxPrice the new maxPrice to be set
   */
  @Modifying(clearAutomatically = true)
  @Query(
      "update Category c set c.label = :label, c.description = :description, c.minPrice = :minPrice, c.maxPrice = :maxPrice where c.type = :type")
  void updateCategoryByType(
      String type, String label, String description, BigDecimal minPrice, BigDecimal maxPrice);

  /**
   * Updates {@link Category} identified by its categoryId.
   *
   * @param categoryId the id of the Category to be updated
   * @param type the new type to be set
   * @param label the new label to be set
   * @param description the new description to be set
   * @param minPrice the new minPrice to be set
   * @param maxPrice the new maxPrice to be set
   */
  @Modifying(clearAutomatically = true)
  @Query(
      "update Category c set c.type = :type, c.label = :label, c.description = :description, c.minPrice = :minPrice, c.maxPrice = :maxPrice where c.categoryId = :categoryId")
  void updateCategoryByCategoryId(
      String categoryId,
      String type,
      String label,
      String description,
      BigDecimal minPrice,
      BigDecimal maxPrice);

  Optional<Category> findCategoryByTypeIgnoreCase(String type);
}
