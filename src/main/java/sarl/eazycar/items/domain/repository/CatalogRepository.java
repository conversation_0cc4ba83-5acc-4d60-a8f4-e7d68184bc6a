package sarl.eazycar.items.domain.repository;

import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import sarl.eazycar.items.domain.entity.Catalog;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@Repository
public interface CatalogRepository extends JpaRepository<Catalog, String> {

  /**
   * update the service and description of a {@link Catalog} identified by its code
   *
   * @param code the code of the catalog to be updated
   * @param service the new service to be set
   * @param description the new description to be set
   */
  @Modifying(clearAutomatically = true)
  @Query(
      "update Catalog c set c.service = :service, c.description = :description where c.code = :code")
  void updateCatalogByCode(
      @Param("code") String code,
      @Param("service") String service,
      @Param("description") String description);

  /**
   * update a {@link Catalog} identified by its id.
   *
   * @param catalogId the id of the catalog to be updated
   * @param code the new code to be set
   * @param service the new service to be set
   * @param description the new description to be set
   */
  @Modifying(clearAutomatically = true)
  @Query(
      "update Catalog c set c.code = :code, c.service = :service, c.description = :description where c.catalogId = :catalogId")
  void updateCatalogByCatalogId(
      @Param("catalogId") String catalogId,
      @Param("code") String code,
      @Param("service") String service,
      @Param("description") String description);

  /**
   * Find a catalog by its code with prices loaded eagerly using named EntityGraph
   *
   * @param catalogCode the code of the catalog to find
   * @return the catalog with its prices loaded
   */
  @EntityGraph(value = "Catalog.withPrices")
  Optional<Catalog> findCatalogByCodeIgnoreCase(String catalogCode);

  /**
   * Find a catalog by its ID with prices loaded eagerly using named EntityGraph
   *
   * @param catalogId the ID of the catalog to find
   * @return the catalog with its prices loaded
   */
  @EntityGraph(value = "Catalog.withPrices")
  @Override
  Optional<Catalog> findById(String catalogId);

  /**
   * Find all catalogs with prices loaded eagerly using named EntityGraph
   *
   * @return all catalogs with their prices loaded
   */
  @EntityGraph(value = "Catalog.withPrices")
  @Override
  List<Catalog> findAll();
}
