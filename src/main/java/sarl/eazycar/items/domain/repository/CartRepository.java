package sarl.eazycar.items.domain.repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import sarl.eazycar.items.domain.entity.Cart;
import sarl.eazycar.items.domain.entity.enums.CartStatus;

@Repository
public interface CartRepository extends JpaRepository<Cart, String> {

  Optional<Cart> findByAccountId(String accountId);

  List<Cart> findByStatus(CartStatus status);

  @Query("SELECT c FROM Cart c WHERE c.accountId = :accountId AND c.status = :status")
  List<Cart> findByAccountIdAndStatus(
      @Param("accountId") String accountId, @Param("status") CartStatus status);

  @Query("SELECT c FROM Cart c WHERE c.createdDate BETWEEN :startDate AND :endDate")
  List<Cart> findByCreatedDateBetween(
      @Param("startDate") Instant startDate, @Param("endDate") Instant endDate);
}
