package sarl.eazycar.items.domain.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import sarl.eazycar.items.domain.entity.Rental;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface RentalRepository extends JpaRepository<Rental, String> {

    List<Rental> findByAccountId(String accountId);

    List<Rental> findByStatus(OrderStatus status);

    @Query("SELECT r FROM Rental r WHERE r.startDate BETWEEN :startDate AND :endDate")
    List<Rental> findByStartDateBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    @Query("SELECT r FROM Rental r WHERE r.endDate BETWEEN :startDate AND :endDate")
    List<Rental> findByEndDateBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    @Query("SELECT r FROM Rental r WHERE r.pickupLocation = :location OR r.dropoffLocation = :location")
    List<Rental> findByLocation(@Param("location") String location);

    @Query("SELECT r FROM Rental r WHERE r.accountId = :accountId AND r.status = :status")
    List<Rental> findByAccountIdAndStatus(@Param("accountId") String accountId, @Param("status") OrderStatus status);
}
