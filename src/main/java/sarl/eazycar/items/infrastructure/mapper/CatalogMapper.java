package sarl.eazycar.items.infrastructure.mapper;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import sarl.eazycar.items.application.dto.ArticleDto;
import sarl.eazycar.items.application.dto.ArticleOptionDto;
import sarl.eazycar.items.application.dto.CatalogDto;
import sarl.eazycar.items.application.dto.PriceDto;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.ArticleOption;
import sarl.eazycar.items.domain.entity.Catalog;
import sarl.eazycar.items.domain.entity.Price;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@Mapper(
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    componentModel = MappingConstants.ComponentModel.SPRING)
public interface CatalogMapper extends EntityMapper<CatalogDto, Catalog> {
  CatalogMapper INSTANCE = Mappers.getMapper(CatalogMapper.class);

  @Override
  @Mapping(target = "prices", source = "prices", qualifiedByName = "priceId", ignore = true)
  CatalogDto toDto(Catalog entity);

  @Named("priceId")
  @Mapping(target = "article", source = "article", qualifiedByName = "articleId")
  @Mapping(target = "articleOption", source = "articleOption", qualifiedByName = "articleOptionId")
  @Mapping(target = "catalog", ignore = true)
  PriceDto toPriceDto(Price price);

  @Named("articleId")
  @BeanMapping(ignoreByDefault = true)
  @Mapping(target = "articleId", source = "articleId")
  ArticleDto toArticleDto(Article article);

  @Named("articleOptionId")
  @BeanMapping(ignoreByDefault = true)
  @Mapping(target = "optionId", source = "optionId")
  @Mapping(target = "optionName", source = "optionName")
  ArticleOptionDto toArticleOptionDto(ArticleOption articleOption);
}
