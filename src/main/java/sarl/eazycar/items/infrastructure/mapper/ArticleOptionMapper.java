package sarl.eazycar.items.infrastructure.mapper;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import sarl.eazycar.items.application.dto.ArticleDto;
import sarl.eazycar.items.application.dto.ArticleOptionDto;
import sarl.eazycar.items.application.dto.CatalogDto;
import sarl.eazycar.items.application.dto.PriceDto;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.ArticleOption;
import sarl.eazycar.items.domain.entity.Catalog;
import sarl.eazycar.items.domain.entity.Price;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@Mapper(
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    componentModel = MappingConstants.ComponentModel.SPRING)
public interface ArticleOptionMapper extends EntityMapper<ArticleOptionDto, ArticleOption> {
  ArticleOptionMapper INSTANCE = Mappers.getMapper(ArticleOptionMapper.class);

  @Override
  @Mapping(target = "prices", source = "prices", qualifiedByName = "priceId", ignore = true)
  ArticleOptionDto toDto(ArticleOption entity);

  @Named("priceId")
  @Mapping(target = "article", source = "article", qualifiedByName = "articleId")
  @Mapping(target = "articleOption", ignore = true)
  @Mapping(target = "catalog", source = "catalog", qualifiedByName = "catalogId")
  PriceDto toPriceDto(Price price);

  @Named("articleId")
  @BeanMapping(ignoreByDefault = true)
  @Mapping(target = "articleId", source = "articleId")
  ArticleDto toArticleDto(Article article);

  @Named("catalogId")
  @BeanMapping(ignoreByDefault = true)
  @Mapping(target = "catalogId", source = "catalogId")
  @Mapping(target = "code", source = "code")
  CatalogDto toCatalogDto(Catalog catalog);
}
