package sarl.eazycar.items.infrastructure.mapper;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import sarl.eazycar.items.application.dto.ArticleDto;
import sarl.eazycar.items.application.dto.ArticleOptionDto;
import sarl.eazycar.items.application.dto.CarDto;
import sarl.eazycar.items.application.dto.CatalogDto;
import sarl.eazycar.items.application.dto.CategoryDto;
import sarl.eazycar.items.application.dto.ImageDto;
import sarl.eazycar.items.application.dto.PriceDto;
import sarl.eazycar.items.application.dto.VideoDto;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.ArticleOption;
import sarl.eazycar.items.domain.entity.Car;
import sarl.eazycar.items.domain.entity.Catalog;
import sarl.eazycar.items.domain.entity.Category;
import sarl.eazycar.items.domain.entity.Image;
import sarl.eazycar.items.domain.entity.Price;
import sarl.eazycar.items.domain.entity.Video;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@Mapper(
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    componentModel = MappingConstants.ComponentModel.SPRING)
public interface CarMapper extends EntityMapper<CarDto, Car> {
  CarMapper INSTANCE = Mappers.getMapper(CarMapper.class);

  @Override
  @Mapping(target = "article", source = "article", qualifiedByName = "articleId")
  CarDto toDto(Car entity);

  @Named("articleId")
  @Mapping(target = "prices", source = "prices", qualifiedByName = "priceId")
  @Mapping(target = "category", source = "category", qualifiedByName = "categoryId")
  @Mapping(target = "images", source = "images", qualifiedByName = "imageId")
  @Mapping(target = "videos", source = "videos", qualifiedByName = "videoId")
  ArticleDto toArticleDto(Article article);

  @Named("priceId")
  @Mapping(target = "article", ignore = true)
  @Mapping(target = "articleOption", source = "articleOption", qualifiedByName = "articleOptionId")
  @Mapping(target = "catalog", source = "catalog", qualifiedByName = "catalogId")
  PriceDto toPriceDto(Price price);

  @Named("categoryId")
  CategoryDto toCategoryDto(Category category);

  @Named("catalogId")
  @BeanMapping(ignoreByDefault = true)
  @Mapping(target = "catalogId", source = "catalogId")
  @Mapping(target = "code", source = "code")
  @Mapping(target = "service", source = "service")
  CatalogDto toCatalogDto(Catalog catalog);

  @Named("articleOptionId")
  @BeanMapping(ignoreByDefault = true)
  @Mapping(target = "optionId", source = "optionId")
  @Mapping(target = "optionName", source = "optionName")
  ArticleOptionDto toArticleOptionDto(ArticleOption articleOption);

  @Named("imageId")
  @Mapping(target = "article", ignore = true)
  ImageDto toImageDto(Image image);

  @Named("videoId")
  @Mapping(target = "article", ignore = true)
  VideoDto toVideoDto(Video video);
}
