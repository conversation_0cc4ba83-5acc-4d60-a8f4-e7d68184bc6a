package sarl.eazycar.items.infrastructure.mapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import sarl.eazycar.items.application.dto.OrderDto;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.Rental;
import sarl.eazycar.items.domain.entity.enums.OrderType;

/** Mapper pour Order */
@Mapper(
    componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderMapper {

  OrderMapper INSTANCE = Mappers.getMapper(OrderMapper.class);

  @Mapping(target = "orderType", expression = "java(getOrderType(order))")
  @Mapping(target = "shippingAddress", expression = "java(getShippingAddress(order))")
  @Mapping(target = "estimatedDeliveryDate", expression = "java(getEstimatedDeliveryDate(order))")
  @Mapping(target = "startDate", expression = "java(getStartDate(order))")
  @Mapping(target = "endDate", expression = "java(getEndDate(order))")
  @Mapping(target = "pickupLocation", expression = "java(getPickupLocation(order))")
  @Mapping(target = "dropoffLocation", expression = "java(getDropoffLocation(order))")
  @Mapping(target = "totalAmount", expression = "java(getTotalAmount(order))")
  OrderDto toDto(Order order);

  @Named("getOrderType")
  default String getOrderType(Order order) {
    if (order instanceof Purchase) {
      return OrderType.PURCHASE.name();
    } else if (order instanceof Rental) {
      return OrderType.RENTAL.name();
    }
    return null;
  }

  @Named("getShippingAddress")
  default String getShippingAddress(Order order) {
    if (order instanceof Purchase purchase) {
      return purchase.getShippingAddress();
    }
    return null;
  }

  @Named("getEstimatedDeliveryDate")
  default LocalDate getEstimatedDeliveryDate(Order order) {
    if (order instanceof Purchase purchase) {
      return purchase.getEstimatedDeliveryDate();
    }
    return null;
  }

  @Named("getStartDate")
  default LocalDateTime getStartDate(Order order) {
    if (order instanceof Rental rental) {
      return rental.getStartDate();
    }
    return null;
  }

  @Named("getEndDate")
  default LocalDateTime getEndDate(Order order) {
    if (order instanceof Rental rental) {
      return rental.getEndDate();
    }
    return null;
  }

  @Named("getPickupLocation")
  default String getPickupLocation(Order order) {
    if (order instanceof Rental rental) {
      return rental.getPickupLocation();
    }
    return null;
  }

  @Named("getDropoffLocation")
  default String getDropoffLocation(Order order) {
    if (order instanceof Rental rental) {
      return rental.getDropoffLocation();
    }
    return null;
  }

  @Named("getTotalAmount")
  default BigDecimal getTotalAmount(Order order) {
    if (order instanceof Purchase purchase) {
      return purchase.getTotalAmount();
    } else if (order instanceof Rental rental) {
      return rental.getTotalAmount();
    }
    return null;
  }
}
