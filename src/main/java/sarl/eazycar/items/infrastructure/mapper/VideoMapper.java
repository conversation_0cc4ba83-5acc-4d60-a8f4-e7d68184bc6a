package sarl.eazycar.items.infrastructure.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import sarl.eazycar.items.application.dto.VideoDto;
import sarl.eazycar.items.domain.entity.Video;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/4/25
 *
 * <AUTHOR> TECHA<PERSON> KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/4/25
 * @project : EazyCar
 */
@Mapper(
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    componentModel = MappingConstants.ComponentModel.SPRING)
public interface VideoMapper extends EntityMapper<VideoDto, Video> {
  VideoMapper INSTANCE = Mappers.getMapper(VideoMapper.class);

  @Override
  @Mapping(target = "article", ignore = true)
  VideoDto toDto(Video entity);
}
