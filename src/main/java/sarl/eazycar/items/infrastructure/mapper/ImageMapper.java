package sarl.eazycar.items.infrastructure.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import sarl.eazycar.items.application.dto.ImageDto;
import sarl.eazycar.items.domain.entity.Image;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/4/25
 *
 * <AUTHOR> TECHA<PERSON> KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/4/25
 * @project : EazyCar
 */
@Mapper(
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    componentModel = MappingConstants.ComponentModel.SPRING)
public interface ImageMapper extends EntityMapper<ImageDto, Image> {
  ImageMapper INSTANCE = Mappers.getMapper(ImageMapper.class);

  @Override
  @Mapping(target = "article", ignore = true)
  ImageDto toDto(Image entity);
}
