package sarl.eazycar.items.infrastructure.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import sarl.eazycar.items.application.dto.ArticleOptionDto;
import sarl.eazycar.items.application.dto.CatalogDto;
import sarl.eazycar.items.application.dto.PriceDto;
import sarl.eazycar.items.domain.entity.ArticleOption;
import sarl.eazycar.items.domain.entity.Catalog;
import sarl.eazycar.items.domain.entity.Price;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@Mapper(
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    componentModel = MappingConstants.ComponentModel.SPRING)
public interface PriceMapper extends EntityMapper<PriceDto, Price> {
  PriceMapper INSTANCE = Mappers.getMapper(PriceMapper.class);

  @Override
  @Mapping(target = "articleOption", source = "articleOption", qualifiedByName = "articleOptionId")
  @Mapping(target = "catalog", source = "catalog", qualifiedByName = "catalogId")
  PriceDto toDto(Price entity);

  @Named("articleOptionId")
  ArticleOptionDto toArticleOptionDto(ArticleOption articleOption);

  @Named("catalogId")
  CatalogDto toCatalogDto(Catalog catalog);
}
