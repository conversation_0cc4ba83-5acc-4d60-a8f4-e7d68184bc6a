package sarl.eazycar.items.infrastructure.mapper;

import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import sarl.eazycar.items.application.dto.InvoiceDto;
import sarl.eazycar.items.domain.entity.Invoice;

/** Mapper pour Invoice */
@Mapper(
    componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    uses = {OrderMapper.class, PaymentMapper.class})
public interface InvoiceMapper {

  InvoiceMapper INSTANCE = Mappers.getMapper(InvoiceMapper.class);

  InvoiceDto toDto(Invoice invoice);

  List<InvoiceDto> toDto(List<Invoice> invoices);
}
