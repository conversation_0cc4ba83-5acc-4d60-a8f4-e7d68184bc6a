package sarl.eazycar.items.infrastructure.factory;

import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.application.rest.request.ImageRequest;
import sarl.eazycar.items.domain.entity.Image;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/4/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/4/25
 * @project : EazyCar
 */
@Component
@RequiredArgsConstructor
public class ImageFactoryImpl implements ImageFactory {
  @Override
  public List<Image> buildImage(ImageRequest request) {
    if (Objects.isNull(request.getImages()) || request.getImages().isEmpty()) {
      throw new FunctionalErrorException("Images must be provided");
    }
    if (Strings.isBlank(request.getArticleId())) {
      throw new FunctionalErrorException("Article id must be provided");
    }
    return request.getImages().stream()
        .map(
            image ->
                Image.builder()
                    .name(image.getName())
                    .path(image.getPath())
                    .format(image.getFormat())
                    .size(image.getSize())
                    .build())
        .toList();
  }
}
