package sarl.eazycar.items.infrastructure.factory;

import java.time.LocalDateTime;
import org.springframework.stereotype.Component;
import sarl.eazycar.items.application.rest.request.OrderRequest;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.Rental;
import sarl.eazycar.items.domain.entity.enums.OrderType;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 9/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 9/3/25
 */
@Component
public class OrderFactoryImpl implements OrderFactory {
  @Override
  public Order buildOrder(OrderRequest request, Article article) {
    Order order;

    if (OrderType.PURCHASE.name().equals(request.getOrderType())) {
      Purchase purchase = new Purchase();
      purchase.setShippingAddress(request.getShippingAddress());
      purchase.setEstimatedDeliveryDate(request.getEstimatedDeliveryDate());
      order = purchase;
    } else if (OrderType.RENTAL.name().equals(request.getOrderType())) {
      Rental rental = new Rental();
      rental.setStartDate(request.getStartDate());
      rental.setEndDate(request.getEndDate());
      rental.setPickupLocation(request.getPickupLocation());
      rental.setDropoffLocation(request.getDropoffLocation());
      order = rental;
    } else {
      throw new IllegalArgumentException("Invalid order type: " + request.getOrderType());
    }

    // Propriétés communes
    order.setOrderDate(LocalDateTime.now());
    order.setQuantity(request.getQuantity());
    order.setUnitPrice(request.getUnitPrice());
    order.setCurrency(request.getCurrency());
    order.setArticle(article);
    order.setPriceId(request.getPriceId());

    return order;
  }
}
