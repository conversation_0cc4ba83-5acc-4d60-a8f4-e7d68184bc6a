package sarl.eazycar.items.infrastructure.factory;

import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import sarl.eazycar.items.application.rest.request.CarRequest;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.ArticleOption;
import sarl.eazycar.items.domain.entity.Car;
import sarl.eazycar.items.domain.entity.Catalog;
import sarl.eazycar.items.domain.entity.Category;
import sarl.eazycar.items.domain.entity.Price;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@Component
@RequiredArgsConstructor
public class CarFactoryImpl implements CarFactory {
  @Override
  public Car buildCar(CarRequest carRequest) {
    if (Objects.isNull(carRequest.getOptions())) {
      carRequest.setOptions(List.of());
    }
    List<Price> prices =
        carRequest.getOptions().stream()
            .map(
                carOptionRequest ->
                    Price.builder()
                        .price(carOptionRequest.getPrice())
                        .priceId(carOptionRequest.getPriceId())
                        .currency(carOptionRequest.getCurrency())
                        .discountPercent(carOptionRequest.getDiscountPercent())
                        .articleOption(
                            ArticleOption.builder()
                                .optionId(carOptionRequest.getArticleOptionId())
                                .build())
                        .catalog(
                            Catalog.builder().catalogId(carOptionRequest.getCatalogId()).build())
                        .build())
            .toList();
    Article article =
        Article.builder()
            .name(carRequest.getName())
            .description(carRequest.getDescription())
            .cityLocation(carRequest.getCityLocation())
            .districtLocation(carRequest.getDistrictLocation())
            .available(carRequest.isAvailable())
            .prices(prices)
            .category(Category.builder().categoryId(carRequest.getCategoryId()).build())
            .build();

    prices.forEach(price -> price.setArticle(article));

    return Car.builder()
        .brand(carRequest.getBrand())
        .model(carRequest.getModel())
        .factoryYear(carRequest.getFactoryYear())
        .article(article)
        .build();
  }
}
