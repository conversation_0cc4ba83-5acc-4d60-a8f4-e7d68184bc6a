package sarl.eazycar.items.infrastructure.factory;

import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.application.rest.request.VideoRequest;
import sarl.eazycar.items.domain.entity.Video;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/4/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/4/25
 * @project : EazyCar
 */
@Component
@RequiredArgsConstructor
public class VideoFactoryImpl implements VideoFactory {
  @Override
  public List<Video> buildVideo(VideoRequest videoRequest) {
    if (Objects.isNull(videoRequest.getVideos()) || videoRequest.getVideos().isEmpty()) {
      throw new FunctionalErrorException("Videos must be provided");
    }
    if (Strings.isBlank(videoRequest.getArticleId())) {
      throw new FunctionalErrorException("Article id must be provided");
    }
    return videoRequest.getVideos().stream()
        .map(
            video ->
                Video.builder()
                    .name(video.getName())
                    .path(video.getPath())
                    .format(video.getFormat())
                    .size(video.getSize())
                    .build())
        .toList();
  }
}
