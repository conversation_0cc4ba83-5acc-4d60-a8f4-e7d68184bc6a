package sarl.eazycar.items.infrastructure.factory;

import java.util.List;
import sarl.eazycar.items.application.rest.request.VideoRequest;
import sarl.eazycar.items.domain.entity.Video;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/4/25
 *
 * <AUTHOR> <PERSON>EC<PERSON><PERSON> KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/4/25
 * @project : EazyCar
 */
public interface VideoFactory {
  List<Video> buildVideo(VideoRequest videoRequest);
}
