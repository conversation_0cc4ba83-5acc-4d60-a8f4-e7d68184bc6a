package sarl.eazycar.items.infrastructure.config;

import java.util.Optional;
import org.springframework.data.domain.AuditorAware;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import sarl.eazycar.items.infrastructure.utils.ConstantsUtils;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/3/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/3/25
 * @project : EazyCar
 */
@Component
public class SpringAuditorAware implements AuditorAware<String> {
  // TODO: This method will be modified when Spring Security is added.
  @Override
  @NonNull
  public Optional<String> getCurrentAuditor() {
    return Optional.of(ConstantsUtils.SYSTEM);
  }
}
