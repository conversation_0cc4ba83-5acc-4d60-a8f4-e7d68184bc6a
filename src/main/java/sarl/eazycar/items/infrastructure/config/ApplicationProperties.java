package sarl.eazycar.items.infrastructure.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Created by TECHAP KEMADJEU AUGUSTIN <<EMAIL>> on 5/5/25
 *
 * <AUTHOR> TECHAP KEMADJEU AUGUSTIN <<EMAIL>>
 * @date : 5/5/25
 * @project : EazyCar
 */
@Getter
@ConfigurationProperties(prefix = "application", ignoreUnknownFields = false)
public class ApplicationProperties {
  private final MediaSize mediaSize = new MediaSize();

  @Getter
  @Setter
  public static class MediaSize {
    private int imageLength;
    private int videoLength;
  }
}
