spring:
  application:
    name: items-app-test

    # -----------------------------------------------------------
    # DATASOURCE
    # -----------------------------------------------------------
    datasource:
      url: jdbc:h2:mem:ITEMS;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
      driverClassName: org.h2.Driver
      username: sa
      password:

    # -----------------------------------------------------------
    # JPA
    # -----------------------------------------------------------
    jpa:
      database-platform: org.hibernate.dialect.H2Dialect
      hibernate:
        ddl-auto: update
      show-sql: true


application:
  media-size:
    image-length: 5
    video-length: 2