package sarl.eazycar.items.domain.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static sarl.eazycar.items.domain.repository.Assertions.assertImageEquals;

import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import sarl.eazycar.items.domain.entity.Image;
import sarl.eazycar.items.infrastructure.config.TestDataBaseConfig;

/** Tests for the ImageRepository class. */
@DataJpaTest
@Import(TestDataBaseConfig.class)
class ImageRepositoryTest {

  @Autowired private ImageRepository imageRepository;

  @Test
  void saveAndFindImage() {
    // Create an image
    Image image =
        Image.builder()
            .name("Test Image")
            .path("/path/to/test/image.jpg")
            .format("jpg")
            .size(1024)
            .build();

    // Save the image
    Image savedImage = imageRepository.save(image);

    // Verify the image was saved with an ID
    assertThat(savedImage.getFileId()).isNotNull();
    assertThat(savedImage.getName()).isEqualTo("Test Image");
    assertThat(savedImage.getPath()).isEqualTo("/path/to/test/image.jpg");
    assertThat(savedImage.getFormat()).isEqualTo("jpg");
    assertThat(savedImage.getSize()).isEqualTo(1024);
  }

  @Test
  void findAllImages() {
    // Clear any existing data
    imageRepository.deleteAll();

    // Create and save multiple images
    Image image1 =
        Image.builder()
            .name("Image 1")
            .path("/path/to/image1.jpg")
            .format("jpg")
            .size(1024)
            .build();

    Image image2 =
        Image.builder()
            .name("Image 2")
            .path("/path/to/image2.png")
            .format("png")
            .size(2048)
            .build();

    imageRepository.saveAll(List.of(image1, image2));

    // Find all images
    List<Image> images = imageRepository.findAll();

    // Verify the correct number of images is returned
    assertThat(images).hasSize(2);

    // Verify the images contain the expected data
    assertThat(images).extracting(Image::getName).containsExactlyInAnyOrder("Image 1", "Image 2");
    assertThat(images).extracting(Image::getFormat).containsExactlyInAnyOrder("jpg", "png");
  }

  @Test
  void findImageById() {
    // Create and save an image
    Image image =
        Image.builder()
            .name("Test Image")
            .path("/path/to/test/image.jpg")
            .format("jpg")
            .size(1024)
            .build();

    Image savedImage = imageRepository.save(image);
    String imageId = savedImage.getFileId();

    // Find the image by ID
    Optional<Image> foundImageOpt = imageRepository.findById(imageId);

    // Verify the image is found and has the correct data
    assertThat(foundImageOpt).isPresent();
    Image foundImage = foundImageOpt.get();
    assertImageEquals(savedImage, foundImage);
  }

  @Test
  void updateExistingImage() {
    // Create and save an image
    Image image =
        Image.builder()
            .name("Original Name")
            .path("/original/path.jpg")
            .format("jpg")
            .size(1024)
            .build();

    Image savedImage = imageRepository.save(image);
    String imageId = savedImage.getFileId();

    // Update the image
    savedImage.setName("Updated Name");
    savedImage.setPath("/updated/path.png");
    savedImage.setFormat("png");
    savedImage.setSize(2048);
    imageRepository.save(savedImage);

    // Find the updated image
    Optional<Image> updatedImageOpt = imageRepository.findById(imageId);

    // Verify the image was updated correctly
    assertThat(updatedImageOpt).isPresent();
    Image updatedImage = updatedImageOpt.get();
    assertThat(updatedImage.getName()).isEqualTo("Updated Name");
    assertThat(updatedImage.getPath()).isEqualTo("/updated/path.png");
    assertThat(updatedImage.getFormat()).isEqualTo("png");
    assertThat(updatedImage.getSize()).isEqualTo(2048);
  }

  @Test
  void deleteImage() {
    // Create and save an image
    Image image =
        Image.builder()
            .name("Test Image")
            .path("/path/to/delete.jpg")
            .format("jpg")
            .size(1024)
            .build();

    Image savedImage = imageRepository.save(image);
    String imageId = savedImage.getFileId();

    // Verify the image exists
    assertThat(imageRepository.findById(imageId)).isPresent();

    // Delete the image
    imageRepository.deleteById(imageId);

    // Verify the image no longer exists
    assertThat(imageRepository.findById(imageId)).isEmpty();
  }
}
