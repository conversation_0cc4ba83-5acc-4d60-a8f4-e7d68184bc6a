package sarl.eazycar.items.domain.repository;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import sarl.eazycar.items.domain.entity.Cart;
import sarl.eazycar.items.domain.entity.enums.CartStatus;
import sarl.eazycar.items.infrastructure.config.TestDataBaseConfig;

/** Test class for CartRepository */
@DataJpaTest
@Import(TestDataBaseConfig.class)
class CartRepositoryTest {

  @Autowired private CartRepository cartRepository;

  @Test
  void save_cart_successfully() {
    // Given
    Cart cart =
        Cart.builder()
            .status(CartStatus.ACTIVE)
            .totalAmount(BigDecimal.valueOf(150.00))
            .accountId("account-123")
            .build();

    // When
    Cart savedCart = cartRepository.save(cart);

    // Then
    assertThat(savedCart).isNotNull();
    assertThat(savedCart.getCartId()).isNotNull();
    assertThat(savedCart.getStatus()).isEqualTo(CartStatus.ACTIVE);
    assertThat(savedCart.getTotalAmount()).isEqualByComparingTo(BigDecimal.valueOf(150.00));
    assertThat(savedCart.getAccountId()).isEqualTo("account-123");
    assertThat(savedCart.getCreatedDate()).isNotNull(); // From AuditEntity
  }

  @Test
  void find_cart_by_account_id() {
    // Given
    String accountId = "account-test-123";
    Cart cart =
        Cart.builder()
            .status(CartStatus.ACTIVE)
            .totalAmount(BigDecimal.valueOf(200.00))
            .accountId(accountId)
            .build();

    cartRepository.save(cart);

    // When
    Optional<Cart> foundCart = cartRepository.findByAccountId(accountId);

    // Then
    assertThat(foundCart).isPresent();
    assertThat(foundCart.get().getAccountId()).isEqualTo(accountId);
    assertThat(foundCart.get().getStatus()).isEqualTo(CartStatus.ACTIVE);
    assertThat(foundCart.get().getTotalAmount()).isEqualByComparingTo(BigDecimal.valueOf(200.00));
  }

  @Test
  void find_carts_by_status() {
    // Given
    Cart activeCart =
        Cart.builder()
            .status(CartStatus.ACTIVE)
            .totalAmount(BigDecimal.valueOf(100.00))
            .accountId("account-1")
            .build();

    Cart checkedOutCart =
        Cart.builder()
            .status(CartStatus.CHECKED_OUT)
            .totalAmount(BigDecimal.valueOf(250.00))
            .accountId("account-2")
            .build();

    cartRepository.save(activeCart);
    cartRepository.save(checkedOutCart);

    // When
    List<Cart> activeCarts = cartRepository.findByStatus(CartStatus.ACTIVE);
    List<Cart> checkedOutCarts = cartRepository.findByStatus(CartStatus.CHECKED_OUT);

    // Then
    assertThat(activeCarts).hasSize(1);
    assertThat(activeCarts.get(0).getStatus()).isEqualTo(CartStatus.ACTIVE);

    assertThat(checkedOutCarts).hasSize(1);
    assertThat(checkedOutCarts.get(0).getStatus()).isEqualTo(CartStatus.CHECKED_OUT);
  }

  @Test
  void find_cart_by_account_id_and_status() {
    // Given
    String accountId = "account-test-456";

    Cart activeCart =
        Cart.builder()
            .status(CartStatus.ACTIVE)
            .totalAmount(BigDecimal.valueOf(100.00))
            .accountId(accountId)
            .build();

    Cart checkedOutCart =
        Cart.builder()
            .status(CartStatus.CHECKED_OUT)
            .totalAmount(BigDecimal.valueOf(250.00))
            .accountId(accountId)
            .build();

    Cart otherAccountCart =
        Cart.builder()
            .status(CartStatus.ACTIVE)
            .totalAmount(BigDecimal.valueOf(75.00))
            .accountId("other-account")
            .build();

    cartRepository.save(activeCart);
    cartRepository.save(checkedOutCart);
    cartRepository.save(otherAccountCart);

    // When
    List<Cart> activeCartForAccount =
        cartRepository.findByAccountIdAndStatus(accountId, CartStatus.ACTIVE);
    List<Cart> checkedOutCartForAccount =
        cartRepository.findByAccountIdAndStatus(accountId, CartStatus.CHECKED_OUT);

    // Then
    assertThat(activeCartForAccount).hasSize(1);
    assertThat(activeCartForAccount.get(0).getAccountId()).isEqualTo(accountId);
    assertThat(activeCartForAccount.get(0).getStatus()).isEqualTo(CartStatus.ACTIVE);

    assertThat(checkedOutCartForAccount).hasSize(1);
    assertThat(checkedOutCartForAccount.get(0).getAccountId()).isEqualTo(accountId);
    assertThat(checkedOutCartForAccount.get(0).getStatus()).isEqualTo(CartStatus.CHECKED_OUT);
  }

  @Test
  void find_carts_by_created_date_between() {
    // Given
    Instant startDate = Instant.now().minusSeconds(10 * 24 * 60 * 60); // 10 days ago
    Instant endDate = Instant.now().minusSeconds(5 * 24 * 60 * 60); // 5 days ago

    Cart cart1 =
        Cart.builder()
            .status(CartStatus.ACTIVE)
            .totalAmount(BigDecimal.valueOf(100.00))
            .accountId("account-1")
            .build();

    Cart cart2 =
        Cart.builder()
            .status(CartStatus.CHECKED_OUT)
            .totalAmount(BigDecimal.valueOf(200.00))
            .accountId("account-2")
            .build();

    cartRepository.save(cart1);
    cartRepository.save(cart2);

    // When
    List<Cart> cartsInRange = cartRepository.findByCreatedDateBetween(startDate, endDate);

    // Then
    // Note: This test assumes carts are created within the current time range
    // In practice, you'd need to control the creation dates more precisely
    assertThat(cartsInRange).isNotNull();
  }

  @Test
  void update_cart_total_amount_and_status() {
    // Given
    Cart cart =
        Cart.builder()
            .status(CartStatus.ACTIVE)
            .totalAmount(BigDecimal.valueOf(100.00))
            .accountId("account-123")
            .build();

    Cart savedCart = cartRepository.save(cart);

    // When
    savedCart.setTotalAmount(BigDecimal.valueOf(250.00));
    savedCart.setStatus(CartStatus.CHECKED_OUT);
    Cart updatedCart = cartRepository.save(savedCart);

    // Then
    assertThat(updatedCart.getCartId()).isEqualTo(savedCart.getCartId());
    assertThat(updatedCart.getTotalAmount()).isEqualByComparingTo(BigDecimal.valueOf(250.00));
    assertThat(updatedCart.getStatus()).isEqualTo(CartStatus.CHECKED_OUT);
  }

  @Test
  void delete_cart() {
    // Given
    Cart cart =
        Cart.builder()
            .status(CartStatus.ACTIVE)
            .totalAmount(BigDecimal.valueOf(100.00))
            .accountId("account-123")
            .build();

    Cart savedCart = cartRepository.save(cart);

    // When
    cartRepository.delete(savedCart);
    Optional<Cart> deletedCart = cartRepository.findById(savedCart.getCartId());

    // Then
    assertThat(deletedCart).isEmpty();
  }

  @Test
  void find_all_carts() {
    // Given
    Cart cart1 =
        Cart.builder()
            .status(CartStatus.ACTIVE)
            .totalAmount(BigDecimal.valueOf(100.00))
            .accountId("account-1")
            .build();

    Cart cart2 =
        Cart.builder()
            .status(CartStatus.CHECKED_OUT)
            .totalAmount(BigDecimal.valueOf(200.00))
            .accountId("account-2")
            .build();

    cartRepository.save(cart1);
    cartRepository.save(cart2);

    // When
    List<Cart> allCarts = cartRepository.findAll();

    // Then
    assertThat(allCarts).hasSize(2);
    assertThat(allCarts)
        .extracting(Cart::getAccountId)
        .containsExactlyInAnyOrder("account-1", "account-2");
  }

  @Test
  void cart_inherits_audit_fields() {
    // Given
    Cart cart =
        Cart.builder()
            .status(CartStatus.ACTIVE)
            .totalAmount(BigDecimal.valueOf(150.00))
            .accountId("account-123")
            .build();

    // When
    Cart savedCart = cartRepository.save(cart);

    // Then
    assertThat(savedCart.getCreatedBy()).isEqualTo("testUser"); // From TestDataBaseConfig
    assertThat(savedCart.getCreatedDate()).isNotNull();
    assertThat(savedCart.getLastModifiedBy()).isEqualTo("testUser");
    assertThat(savedCart.getLastModifiedDate()).isNotNull();
  }

  @Test
  void multiple_accounts_can_have_active_carts() {
    // Given
    Cart cart1 =
        Cart.builder()
            .status(CartStatus.ACTIVE)
            .totalAmount(BigDecimal.valueOf(100.00))
            .accountId("account-1")
            .build();

    Cart cart2 =
        Cart.builder()
            .status(CartStatus.ACTIVE)
            .totalAmount(BigDecimal.valueOf(200.00))
            .accountId("account-2")
            .build();

    cartRepository.save(cart1);
    cartRepository.save(cart2);

    // When
    Optional<Cart> cart1Found = cartRepository.findByAccountId("account-1");
    Optional<Cart> cart2Found = cartRepository.findByAccountId("account-2");

    // Then
    assertThat(cart1Found).isPresent();
    assertThat(cart1Found.get().getAccountId()).isEqualTo("account-1");

    assertThat(cart2Found).isPresent();
    assertThat(cart2Found.get().getAccountId()).isEqualTo("account-2");
  }
}
