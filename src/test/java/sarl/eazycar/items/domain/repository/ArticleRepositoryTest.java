package sarl.eazycar.items.domain.repository;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import sarl.eazycar.items.domain.entity.*;
import sarl.eazycar.items.infrastructure.config.TestDataBaseConfig;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@DataJpaTest
@Import(TestDataBaseConfig.class)
class ArticleRepositoryTest {

  @Autowired private ArticleRepository articleRepository;

  @Autowired private CategoryRepository categoryRepository;

  @Test
  void save_article_successfully() {
    // Given
    Article article =
        Article.builder()
            .name("Test Article")
            .description("Test Description")
            .cityLocation("Test City")
            .districtLocation("Test District")
            .available(true)
            .visible(true)
            .build();

    // When
    Article savedArticle = articleRepository.save(article);

    // Then
    assertThat(savedArticle).isNotNull();
    assertThat(savedArticle.getArticleId()).isNotNull();
    assertThat(savedArticle.getName()).isEqualTo("Test Article");
    assertThat(savedArticle.getDescription()).isEqualTo("Test Description");
    assertThat(savedArticle.getCityLocation()).isEqualTo("Test City");
    assertThat(savedArticle.getDistrictLocation()).isEqualTo("Test District");
    assertThat(savedArticle.isAvailable()).isTrue();
    assertThat(savedArticle.isVisible()).isTrue();
  }

  @Test
  void find_article_by_id() {
    // Given
    Article article =
        Article.builder()
            .name("Test Article")
            .description("Test Description")
            .cityLocation("Test City")
            .districtLocation("Test District")
            .available(true)
            .visible(true)
            .build();
    Article savedArticle = articleRepository.save(article);

    // When
    Optional<Article> foundArticleOptional =
        articleRepository.findById(savedArticle.getArticleId());

    // Then
    assertThat(foundArticleOptional).isPresent();
    Article foundArticle = foundArticleOptional.get();
    assertThat(foundArticle.getArticleId()).isEqualTo(savedArticle.getArticleId());
    assertThat(foundArticle.getName()).isEqualTo("Test Article");
    assertThat(foundArticle.getDescription()).isEqualTo("Test Description");
    assertThat(foundArticle.getCityLocation()).isEqualTo("Test City");
    assertThat(foundArticle.getDistrictLocation()).isEqualTo("Test District");
    assertThat(foundArticle.isAvailable()).isTrue();
    assertThat(foundArticle.isVisible()).isTrue();
  }

  @Test
  void update_existing_article() {
    // Given
    Article article =
        Article.builder()
            .name("Test Article")
            .description("Test Description")
            .cityLocation("Test City")
            .districtLocation("Test District")
            .available(true)
            .visible(true)
            .build();
    Article savedArticle = articleRepository.save(article);

    // When
    savedArticle.setName("Updated Article");
    savedArticle.setDescription("Updated Description");
    savedArticle.setAvailable(false);
    Article updatedArticle = articleRepository.save(savedArticle);

    // Then
    assertThat(updatedArticle).isNotNull();
    assertThat(updatedArticle.getArticleId()).isEqualTo(savedArticle.getArticleId());
    assertThat(updatedArticle.getName()).isEqualTo("Updated Article");
    assertThat(updatedArticle.getDescription()).isEqualTo("Updated Description");
    assertThat(updatedArticle.isAvailable()).isFalse();
    assertThat(updatedArticle.isVisible()).isTrue();
  }

  @Test
  void delete_article() {
    // Given
    Article article =
        Article.builder()
            .name("Test Article")
            .description("Test Description")
            .cityLocation("Test City")
            .districtLocation("Test District")
            .available(true)
            .visible(true)
            .build();
    Article savedArticle = articleRepository.save(article);

    // When
    articleRepository.delete(savedArticle);
    Optional<Article> deletedArticleOptional =
        articleRepository.findById(savedArticle.getArticleId());

    // Then
    assertThat(deletedArticleOptional).isEmpty();
  }

  @Test
  void find_all_articles() {
    // Given
    Article article1 =
        Article.builder()
            .name("Test Article 1")
            .description("Test Description 1")
            .cityLocation("Test City 1")
            .districtLocation("Test District 1")
            .available(true)
            .visible(true)
            .build();

    Article article2 =
        Article.builder()
            .name("Test Article 2")
            .description("Test Description 2")
            .cityLocation("Test City 2")
            .districtLocation("Test District 2")
            .available(false)
            .visible(true)
            .build();

    articleRepository.save(article1);
    articleRepository.save(article2);

    // When
    List<Article> articles = articleRepository.findAll();

    // Then
    assertThat(articles).hasSize(2);
    assertThat(articles)
        .extracting(Article::getName)
        .containsExactlyInAnyOrder("Test Article 1", "Test Article 2");
  }

  @Test
  void article_with_relationships() {
    // Given
    Category category =
        Category.builder()
            .type("Test Type")
            .label("Test Label")
            .description("Test Category Description")
            .minPrice(BigDecimal.valueOf(100))
            .maxPrice(BigDecimal.valueOf(1000))
            .build();

    Category savedCategory = categoryRepository.save(category);

    Article article =
        Article.builder()
            .name("Test Article")
            .description("Test Description")
            .cityLocation("Test City")
            .districtLocation("Test District")
            .available(true)
            .visible(true)
            .category(savedCategory)
            .prices(new ArrayList<>())
            .images(new ArrayList<>())
            .videos(new ArrayList<>())
            .build();

    Price price =
        Price.builder()
            .price(BigDecimal.valueOf(500))
            .currency("USD")
            .discountPercent(BigDecimal.valueOf(10))
            .build();

    Video video =
        Video.builder()
            .name("Test Video")
            .path("/videos/test.mp4")
            .format("MP4")
            .size(1024)
            .build();

    Image image =
        Image.builder().name("Test Image").path("/images/test.jpg").format("JPG").size(512).build();

    article.addPrice(price);
    article.addVideo(video);
    article.addImage(image);

    // When
    Article savedArticle = articleRepository.save(article);
    Optional<Article> foundArticleOptional =
        articleRepository.findById(savedArticle.getArticleId());

    // Then
    assertThat(foundArticleOptional).isPresent();
    Article foundArticle = foundArticleOptional.get();

    assertThat(foundArticle.getCategory()).isNotNull();
    assertThat(foundArticle.getCategory().getCategoryId()).isEqualTo(savedCategory.getCategoryId());

    assertThat(foundArticle.getPrices()).hasSize(1);
    assertThat(foundArticle.getPrices().get(0).getPrice())
        .isEqualByComparingTo(BigDecimal.valueOf(500));

    assertThat(foundArticle.getVideos()).hasSize(1);
    assertThat(foundArticle.getVideos().get(0).getName()).isEqualTo("Test Video");

    assertThat(foundArticle.getImages()).hasSize(1);
    assertThat(foundArticle.getImages().get(0).getName()).isEqualTo("Test Image");
  }
}
