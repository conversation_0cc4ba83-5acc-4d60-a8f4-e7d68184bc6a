package sarl.eazycar.items.domain.repository;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Contract;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.enums.ContractStatus;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.infrastructure.config.TestDataBaseConfig;

/** Test class for ContractRepository */
@DataJpaTest
@Import(TestDataBaseConfig.class)
class ContractRepositoryTest {

  @Autowired private ContractRepository contractRepository;

  @Autowired private ArticleRepository articleRepository;

  @Autowired private OrderRepository orderRepository;

  private Article createArticle(String articleId) {
    Article article = new Article();
    article.setArticleId(articleId);
    article.setName("Test Article");
    article.setDescription("Test Description");
    return articleRepository.save(article);
  }

  private Order createOrder(String orderId) {
    Article article = createArticle("article-" + orderId);

    Purchase order = new Purchase();
    order.setOrderId(orderId);
    order.setOrderDate(LocalDateTime.now());
    order.setStatus(OrderStatus.CONFIRMED);
    order.setCurrency("EUR");
    order.setQuantity(1);
    order.setUnitPrice(BigDecimal.valueOf(100.00));
    order.setArticle(article);
    order.setAccountId("account-123");
    order.setShippingAddress("Test Address");
    order.setTotalAmount(BigDecimal.valueOf(100.00));
    return orderRepository.save(order);
  }

  @Test
  void save_contract_successfully() {
    // Given
    Order order = createOrder(null);
    Contract contract =
        Contract.builder()
            .documentUrl("https://example.com/contracts/contract-123.pdf")
            .signatureDate(null)
            .status(ContractStatus.PENDING_SIGNATURE)
            .order(order)
            .build();

    // When
    Contract savedContract = contractRepository.save(contract);

    // Then
    assertThat(savedContract).isNotNull();
    assertThat(savedContract.getContractId()).isNotNull();
    assertThat(savedContract.getDocumentUrl())
        .isEqualTo("https://example.com/contracts/contract-123.pdf");
    assertThat(savedContract.getSignatureDate()).isNull();
    assertThat(savedContract.getStatus()).isEqualTo(ContractStatus.PENDING_SIGNATURE);
    assertThat(savedContract.getOrder().getOrderId()).isEqualTo(order.getOrderId());
    assertThat(savedContract.getCreatedDate()).isNotNull(); // From AuditEntity
  }

  @Test
  void find_contract_by_order_id() {
    // Given
    Order order = createOrder(null);
    Contract contract =
        Contract.builder()
            .documentUrl("https://example.com/contracts/contract-456.pdf")
            .signatureDate(LocalDateTime.now())
            .status(ContractStatus.SIGNED)
            .order(order)
            .build();

    contractRepository.save(contract);

    // When
    Optional<Contract> foundContract = contractRepository.findByOrderOrderId(order.getOrderId());

    // Then
    assertThat(foundContract).isPresent();
    assertThat(foundContract.get().getOrder().getOrderId()).isEqualTo(order.getOrderId());
    assertThat(foundContract.get().getStatus()).isEqualTo(ContractStatus.SIGNED);
    assertThat(foundContract.get().getSignatureDate()).isNotNull();
  }

  @Test
  void find_contracts_by_status() {
    // Given
    Order order1 = createOrder("order-1");
    Contract pendingContract =
        Contract.builder()
            .documentUrl("https://example.com/contracts/contract-1.pdf")
            .signatureDate(null)
            .status(ContractStatus.PENDING_SIGNATURE)
            .order(order1)
            .build();

    Order order2 = createOrder("order-2");
    Contract signedContract =
        Contract.builder()
            .documentUrl("https://example.com/contracts/contract-2.pdf")
            .signatureDate(LocalDateTime.now())
            .status(ContractStatus.SIGNED)
            .order(order2)
            .build();

    contractRepository.save(pendingContract);
    contractRepository.save(signedContract);

    // When
    List<Contract> pendingContracts =
        contractRepository.findByStatus(ContractStatus.PENDING_SIGNATURE);
    List<Contract> signedContracts = contractRepository.findByStatus(ContractStatus.SIGNED);

    // Then
    assertThat(pendingContracts).hasSize(1);
    assertThat(pendingContracts.get(0).getStatus()).isEqualTo(ContractStatus.PENDING_SIGNATURE);

    assertThat(signedContracts).hasSize(1);
    assertThat(signedContracts.get(0).getStatus()).isEqualTo(ContractStatus.SIGNED);
  }

  @Test
  void find_contracts_by_created_date_between() {
    // Given
    Instant startDate = Instant.now().minusSeconds(10 * 24 * 60 * 60); // 10 days ago
    Instant endDate = Instant.now().minusSeconds(5 * 24 * 60 * 60); // 5 days ago

    Order order1 = createOrder("order-1");
    Contract contract1 =
        Contract.builder()
            .documentUrl("https://example.com/contracts/contract-1.pdf")
            .signatureDate(null)
            .status(ContractStatus.PENDING_SIGNATURE)
            .order(order1)
            .build();

    contractRepository.save(contract1);

    // When
    List<Contract> contractsInRange =
        contractRepository.findByCreatedDateBetween(startDate, endDate);

    // Then
    // This test assumes contracts are created within the current time range
    // In practice, you'd need to control the creation dates more precisely
    assertThat(contractsInRange).isNotNull();
  }

  @Test
  void find_contracts_by_signature_date_between() {
    // Given
    LocalDateTime startDate = LocalDateTime.now().minusDays(10);
    LocalDateTime endDate = LocalDateTime.now().minusDays(5);

    Order order1 = createOrder("order-1");
    Contract contract1 =
        Contract.builder()
            .documentUrl("https://example.com/contracts/contract-1.pdf")
            .signatureDate(LocalDateTime.now().minusDays(7)) // Within range
            .status(ContractStatus.SIGNED)
            .order(order1)
            .build();

    Order order2 = createOrder("order-2");
    Contract contract2 =
        Contract.builder()
            .documentUrl("https://example.com/contracts/contract-2.pdf")
            .signatureDate(LocalDateTime.now().minusDays(8)) // Within range
            .status(ContractStatus.SIGNED)
            .order(order2)
            .build();

    contractRepository.save(contract1);
    contractRepository.save(contract2);

    // When
    List<Contract> contractsInRange =
        contractRepository.findBySignatureDateBetween(startDate, endDate);

    // Then
    assertThat(contractsInRange).hasSize(2);
    assertThat(contractsInRange)
        .extracting(Contract::getSignatureDate)
        .allMatch(date -> date != null && !date.isBefore(startDate) && !date.isAfter(endDate));
  }
}
