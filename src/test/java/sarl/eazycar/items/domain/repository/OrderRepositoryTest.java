package sarl.eazycar.items.domain.repository;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Cart;
import sarl.eazycar.items.domain.entity.Order;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.Rental;
import sarl.eazycar.items.domain.entity.enums.CartStatus;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.infrastructure.config.TestDataBaseConfig;

/** Test class for OrderRepository */
@DataJpaTest
@Import(TestDataBaseConfig.class)
class OrderRepositoryTest {

  @Autowired private OrderRepository orderRepository;

  @Autowired private ArticleRepository articleRepository;

  private Article createArticle(String articleId) {
    Article article = new Article();
    article.setArticleId(articleId);
    article.setName("Test Article");
    article.setDescription("Test Description");
    return articleRepository.save(article);
  }

  private Cart createCart(String cartId, String accountId) {
    Cart cart = new Cart();
    cart.setCartId(cartId);
    cart.setAccountId(accountId);
    cart.setStatus(CartStatus.ACTIVE);
    cart.setTotalAmount(BigDecimal.ZERO);
    return cart;
  }

  private Purchase createPurchase(
      LocalDateTime orderDate,
      OrderStatus status,
      String currency,
      Integer quantity,
      BigDecimal unitPrice,
      String articleId,
      String accountId,
      String cartId,
      String shippingAddress,
      BigDecimal totalAmount) {
    Article article = createArticle(articleId);
    Cart cart = cartId != null ? createCart(cartId, accountId) : null;

    Purchase purchase = new Purchase();
    purchase.setOrderDate(orderDate);
    purchase.setStatus(status);
    purchase.setCurrency(currency);
    purchase.setQuantity(quantity);
    purchase.setUnitPrice(unitPrice);
    purchase.setArticle(article);
    purchase.setAccountId(accountId);
    purchase.setCart(cart);
    purchase.setShippingAddress(shippingAddress);
    purchase.setTotalAmount(totalAmount);
    return purchase;
  }

  private Rental createRental(
      LocalDateTime orderDate,
      OrderStatus status,
      String currency,
      Integer quantity,
      BigDecimal unitPrice,
      String articleId,
      String accountId,
      String cartId,
      LocalDateTime startDate,
      LocalDateTime endDate,
      String pickupLocation,
      String dropoffLocation,
      BigDecimal totalAmount) {
    Article article = createArticle(articleId);
    Cart cart = cartId != null ? createCart(cartId, accountId) : null;

    Rental rental = new Rental();
    rental.setOrderDate(orderDate);
    rental.setStatus(status);
    rental.setCurrency(currency);
    rental.setQuantity(quantity);
    rental.setUnitPrice(unitPrice);
    rental.setArticle(article);
    rental.setAccountId(accountId);
    rental.setCart(cart);
    rental.setStartDate(startDate);
    rental.setEndDate(endDate);
    rental.setPickupLocation(pickupLocation);
    rental.setDropoffLocation(dropoffLocation);
    rental.setTotalAmount(totalAmount);
    return rental;
  }

  @Test
  void save_purchase_order_successfully() {
    // Given
    Purchase purchase =
        createPurchase(
            LocalDateTime.now(),
            OrderStatus.PENDING,
            "USD",
            2,
            BigDecimal.valueOf(100.00),
            null,
            "account-456",
            null,
            "123 Test Street",
            BigDecimal.valueOf(200.00));

    // When
    Order savedOrder = orderRepository.save(purchase);

    // Then
    assertThat(savedOrder).isNotNull();
    assertThat(savedOrder.getOrderId()).isNotNull();
    assertThat(savedOrder.getStatus()).isEqualTo(OrderStatus.PENDING);
    assertThat(savedOrder.getCurrency()).isEqualTo("USD");
    assertThat(savedOrder.getQuantity()).isEqualTo(2);
    assertThat(savedOrder.getUnitPrice()).isEqualByComparingTo(BigDecimal.valueOf(100.00));
    assertThat(savedOrder.getArticle().getArticleId()).isNotNull();
    assertThat(savedOrder.getAccountId()).isEqualTo("account-456");
  }

  @Test
  void save_rental_order_successfully() {
    // Given
    Rental rental =
        createRental(
            LocalDateTime.now(),
            OrderStatus.CONFIRMED,
            "EUR",
            1,
            BigDecimal.valueOf(50.00),
            null,
            "account-123",
            null,
            LocalDateTime.now().plusDays(1),
            LocalDateTime.now().plusDays(3),
            "Location A",
            "Location B",
            BigDecimal.valueOf(100.00));

    // When
    Order savedOrder = orderRepository.save(rental);

    // Then
    assertThat(savedOrder).isNotNull();
    assertThat(savedOrder.getOrderId()).isNotNull();
    assertThat(savedOrder.getStatus()).isEqualTo(OrderStatus.CONFIRMED);
    assertThat(savedOrder.getCurrency()).isEqualTo("EUR");
    assertThat(savedOrder.getArticle().getArticleId()).isNotNull();
    assertThat(savedOrder.getAccountId()).isEqualTo("account-123");
  }

  @Test
  void find_orders_by_account_id() {
    // Given
    String accountId = "account-test-123";

    Purchase purchase1 =
        createPurchase(
            LocalDateTime.now(),
            OrderStatus.PENDING,
            "USD",
            1,
            BigDecimal.valueOf(50.00),
            "article-1",
            accountId,
            null,
            "Address 1",
            BigDecimal.valueOf(100.00));

    Purchase purchase2 =
        createPurchase(
            LocalDateTime.now(),
            OrderStatus.CONFIRMED,
            "USD",
            2,
            BigDecimal.valueOf(75.00),
            "article-2",
            accountId,
            null,
            "Address 2",
            BigDecimal.valueOf(150.00));

    orderRepository.save(purchase1);
    orderRepository.save(purchase2);

    // When
    List<Order> orders = orderRepository.findByAccountId(accountId);

    // Then
    assertThat(orders).hasSize(2);
    assertThat(orders).extracting(Order::getAccountId).containsOnly(accountId);
  }

  @Test
  void find_orders_by_status() {
    // Given
    Purchase pendingOrder =
        createPurchase(
            LocalDateTime.now(),
            OrderStatus.PENDING,
            "USD",
            1,
            BigDecimal.valueOf(50.00),
            "article-1",
            "account-1",
            null,
            "Address 1",
            BigDecimal.valueOf(100.00));

    Purchase confirmedOrder =
        createPurchase(
            LocalDateTime.now(),
            OrderStatus.CONFIRMED,
            "USD",
            2,
            BigDecimal.valueOf(75.00),
            "article-2",
            "account-2",
            null,
            "Address 2",
            BigDecimal.valueOf(150.00));

    orderRepository.save(pendingOrder);
    orderRepository.save(confirmedOrder);

    // When
    List<Order> pendingOrders = orderRepository.findByStatus(OrderStatus.PENDING);
    List<Order> confirmedOrders = orderRepository.findByStatus(OrderStatus.CONFIRMED);

    // Then
    assertThat(pendingOrders).hasSize(1);
    assertThat(pendingOrders.get(0).getStatus()).isEqualTo(OrderStatus.PENDING);

    assertThat(confirmedOrders).hasSize(1);
    assertThat(confirmedOrders.get(0).getStatus()).isEqualTo(OrderStatus.CONFIRMED);
  }

  @Test
  void find_orders_by_account_id_and_status() {
    // Given
    String accountId = "account-test-456";

    Purchase pendingOrder =
        createPurchase(
            LocalDateTime.now(),
            OrderStatus.PENDING,
            "USD",
            1,
            BigDecimal.valueOf(50.00),
            "article-1",
            accountId,
            null,
            "Address 1",
            BigDecimal.valueOf(100.00));

    Purchase confirmedOrder =
        createPurchase(
            LocalDateTime.now(),
            OrderStatus.CONFIRMED,
            "USD",
            2,
            BigDecimal.valueOf(75.00),
            "article-2",
            accountId,
            null,
            "Address 2",
            BigDecimal.valueOf(150.00));

    orderRepository.save(pendingOrder);
    orderRepository.save(confirmedOrder);

    // When
    List<Order> pendingOrdersForAccount =
        orderRepository.findByAccountIdAndStatus(accountId, OrderStatus.PENDING);

    // Then
    assertThat(pendingOrdersForAccount).hasSize(1);
    assertThat(pendingOrdersForAccount.get(0).getAccountId()).isEqualTo(accountId);
    assertThat(pendingOrdersForAccount.get(0).getStatus()).isEqualTo(OrderStatus.PENDING);
  }
}
