package sarl.eazycar.items.domain.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static sarl.eazycar.items.domain.repository.Assertions.assertVideoEquals;

import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import sarl.eazycar.items.domain.entity.Video;
import sarl.eazycar.items.infrastructure.config.TestDataBaseConfig;

/** Tests for VideoRepository */
@DataJpaTest
@Import(TestDataBaseConfig.class)
class VideoRepositoryTest {

  @Autowired private VideoRepository videoRepository;

  @Autowired private ArticleRepository articleRepository;

  @Test
  @DisplayName("Should save video successfully")
  void saveVideoSuccessfully() {
    // Given
    Video video =
        Video.builder()
            .name("Test Video")
            .path("/videos/test-video.mp4")
            .format("mp4")
            .size(1024)
            .build();

    // When
    Video savedVideo = videoRepository.save(video);

    // Then
    assertThat(savedVideo).isNotNull();
    assertThat(savedVideo.getFileId()).isNotNull();
    assertVideoEquals(video, savedVideo);
    assertThat(savedVideo.getCreatedBy()).isEqualTo("testUser");
    assertThat(savedVideo.getLastModifiedBy()).isEqualTo("testUser");
    assertThat(savedVideo.getCreatedDate()).isNotNull();
    assertThat(savedVideo.getLastModifiedDate()).isNotNull();
  }

  @Test
  @DisplayName("Should find video by ID")
  void findVideoById() {
    // Given
    Video video =
        Video.builder()
            .name("Test Video")
            .path("/videos/test-video.mp4")
            .format("mp4")
            .size(1024)
            .build();
    Video savedVideo = videoRepository.save(video);

    // When
    Optional<Video> foundVideo = videoRepository.findById(savedVideo.getFileId());

    // Then
    assertThat(foundVideo).isPresent();
    assertVideoEquals(savedVideo, foundVideo.get());
  }

  @Test
  @DisplayName("Should find all videos")
  void findAllVideos() {
    // Given
    Video video1 =
        Video.builder()
            .name("Test Video 1")
            .path("/videos/test-video1.mp4")
            .format("mp4")
            .size(1024)
            .build();

    Video video2 =
        Video.builder()
            .name("Test Video 2")
            .path("/videos/test-video2.mp4")
            .format("mp4")
            .size(2048)
            .build();

    videoRepository.save(video1);
    videoRepository.save(video2);

    // When
    List<Video> videos = videoRepository.findAll();

    // Then
    assertThat(videos).isNotEmpty();
    assertThat(videos.size()).isGreaterThanOrEqualTo(2);
  }

  @Test
  @DisplayName("Should delete video")
  void deleteVideo() {
    // Given
    Video video =
        Video.builder()
            .name("Test Video")
            .path("/videos/test-video.mp4")
            .format("mp4")
            .size(1024)
            .build();
    Video savedVideo = videoRepository.save(video);

    // When
    videoRepository.delete(savedVideo);
    Optional<Video> foundVideo = videoRepository.findById(savedVideo.getFileId());

    // Then
    assertThat(foundVideo).isEmpty();
  }

  @Test
  @DisplayName("Should update video properties")
  void updateVideoProperties() {
    // Given
    Video video =
        Video.builder()
            .name("Test Video")
            .path("/videos/test-video.mp4")
            .format("mp4")
            .size(1024)
            .build();
    Video savedVideo = videoRepository.save(video);

    // When
    savedVideo.setName("Updated Video Name");
    savedVideo.setPath("/videos/updated-video.mp4");
    savedVideo.setSize(2048);
    Video updatedVideo = videoRepository.save(savedVideo);

    // Then
    assertThat(updatedVideo).isNotNull();
    assertThat(updatedVideo.getName()).isEqualTo("Updated Video Name");
    assertThat(updatedVideo.getPath()).isEqualTo("/videos/updated-video.mp4");
    assertThat(updatedVideo.getSize()).isEqualTo(2048);
    assertThat(updatedVideo.getLastModifiedDate()).isNotNull();
  }
}
