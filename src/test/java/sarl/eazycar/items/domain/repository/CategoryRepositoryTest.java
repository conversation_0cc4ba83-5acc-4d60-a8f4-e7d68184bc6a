package sarl.eazycar.items.domain.repository;

import static java.math.BigDecimal.valueOf;
import static org.assertj.core.api.Assertions.assertThat;
import static sarl.eazycar.items.domain.repository.Assertions.assertCategoryEquals;

import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import sarl.eazycar.items.domain.entity.Category;
import sarl.eazycar.items.infrastructure.config.TestDataBaseConfig;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@DataJpaTest
@Import(TestDataBaseConfig.class)
class CategoryRepositoryTest {

  @Autowired private CategoryRepository categoryRepository;

  @Test
  void save_category_object_should_return_category_entity() {
    var category =
        Category.builder()
            .type("type")
            .description("description")
            .label("label")
            .minPrice(valueOf(1000))
            .maxPrice(valueOf(2000))
            .build();

    Category savedCategory = categoryRepository.save(category);

    assertCategoryEquals(category, savedCategory);
  }

  @Test
  void update_category_object_should_return_category_entity() {
    categoryRepository.save(
        Category.builder()
            .type("type")
            .description("description")
            .label("label")
            .minPrice(valueOf(1000))
            .maxPrice(valueOf(2000))
            .build());
    categoryRepository.updateCategoryByType(
        "type", "newLabel", "newDescription", valueOf(2000), valueOf(3000));
    Optional<Category> foundCategory = categoryRepository.findCategoryByTypeIgnoreCase("type");

    assertThat(foundCategory).isPresent();
    Category updatedCategory = foundCategory.get();
    assertThat(updatedCategory.getLabel()).isEqualTo("newLabel");
    assertThat(updatedCategory.getDescription()).isEqualTo("newDescription");
  }

  @Test
  void find_category_by_label_should_return_category() {
    var category =
        Category.builder()
            .type("type")
            .description("description")
            .label("label")
            .minPrice(valueOf(1000))
            .maxPrice(valueOf(2000))
            .build();

    categoryRepository.save(category);

    Optional<Category> foundCategory = categoryRepository.findCategoryByTypeIgnoreCase("type");

    assertThat(foundCategory).isPresent();
    assertCategoryEquals(category, foundCategory.get());
  }

  @Test
  void find_all_category_should_return_list_of_category() {
    var category1 =
        Category.builder()
            .type("type1")
            .description("description1")
            .label("label1")
            .minPrice(valueOf(1000))
            .maxPrice(valueOf(2000))
            .build();

    var category2 =
        Category.builder()
            .type("type2")
            .description("description2")
            .label("label2")
            .minPrice(valueOf(2000))
            .maxPrice(valueOf(3000))
            .build();

    categoryRepository.save(category1);
    categoryRepository.save(category2);

    var categories = categoryRepository.findAll();

    assertThat(categories).isNotEmpty();
    assertThat(categories).size().isEqualTo(2);
    assertThat(categories).contains(category1, category2);
  }
}
