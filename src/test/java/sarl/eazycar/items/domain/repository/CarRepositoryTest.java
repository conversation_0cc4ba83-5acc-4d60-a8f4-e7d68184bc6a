package sarl.eazycar.items.domain.repository;

import static java.math.BigDecimal.valueOf;
import static org.assertj.core.api.Assertions.assertThat;
import static sarl.eazycar.items.domain.repository.Assertions.assertCarEquals;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.ArticleOption;
import sarl.eazycar.items.domain.entity.Car;
import sarl.eazycar.items.domain.entity.Catalog;
import sarl.eazycar.items.domain.entity.Category;
import sarl.eazycar.items.domain.entity.Image;
import sarl.eazycar.items.domain.entity.Price;
import sarl.eazycar.items.domain.entity.Video;
import sarl.eazycar.items.infrastructure.config.TestDataBaseConfig;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@DataJpaTest
@Import(TestDataBaseConfig.class)
class CarRepositoryTest {

  @Autowired private CarRepository carRepository;

  @Autowired private ArticleOptionRepository articleOptionRepository;

  @Autowired private CatalogRepository catalogRepository;

  @Autowired private CategoryRepository categoryRepository;

  @Test
  void create_article_car_with_related_entities() {
    // Build and save Category
    var category =
        Category.builder()
            .type("type")
            .description("description")
            .label("label")
            .minPrice(valueOf(1000))
            .maxPrice(valueOf(2000))
            .build();
    categoryRepository.save(category);

    // Build and save Catalog
    var catalog =
        Catalog.builder().code("code").description("description").service("service").build();
    catalogRepository.save(catalog);

    // Build and save ArticleOption
    var articleOption =
        ArticleOption.builder()
            .optionName("optionName")
            .optionDescription("optionDescription")
            .build();
    articleOptionRepository.save(articleOption);

    // Build Price
    Price price =
        Price.builder()
            .price(BigDecimal.valueOf(1500))
            .currency("USD")
            .discountPercent(BigDecimal.valueOf(10))
            .catalog(catalog)
            .articleOption(articleOption)
            .build();

    // Build Image
    Image image =
        Image.builder().name("Image Name").path("/path/to/image").format("jpg").size(1024).build();

    // Build Video
    Video video =
        Video.builder().name("Video Name").path("/path/to/video").format("mp4").size(2048).build();

    // Build and save article Car
    Article article =
        Article.builder()
            .name("Car name")
            .description("Car description")
            .cityLocation("City location")
            .districtLocation("District location")
            .available(true)
            .visible(true)
            .category(category)
            .prices(List.of(price))
            .images(List.of(image))
            .videos(List.of(video))
            .build();
    Car car =
        Car.builder().brand("brand").model("model").factoryYear(2021).article(article).build();
    carRepository.save(car);

    // Retrieve article Car and related entities
    Optional<Car> foundCarOpt = carRepository.findById(car.getCarId());
    assertThat(foundCarOpt).isPresent();

    Car foundCar = foundCarOpt.get();

    // Assertions
    assertCarEquals(car, foundCar);
  }
}
