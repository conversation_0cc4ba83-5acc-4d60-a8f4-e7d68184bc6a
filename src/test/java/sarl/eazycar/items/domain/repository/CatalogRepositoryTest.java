package sarl.eazycar.items.domain.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static sarl.eazycar.items.domain.repository.Assertions.assertCatalogEquals;

import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import sarl.eazycar.items.domain.entity.Catalog;
import sarl.eazycar.items.infrastructure.config.TestDataBaseConfig;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@DataJpaTest
@Import(TestDataBaseConfig.class)
class CatalogRepositoryTest {

  @Autowired private CatalogRepository catalogRepository;

  @Test
  void save_catalog_object_should_return_catalog_entity() {
    var catalog =
        Catalog.builder().code("code").description("description").service("service").build();

    Catalog savedCatalog = catalogRepository.save(catalog);

    assertCatalogEquals(catalog, savedCatalog);
  }

  @Test
  void update_catalog_object_should_return_catalog_entity() {
    catalogRepository.save(
        Catalog.builder().code("code").description("description").service("service").build());
    catalogRepository.updateCatalogByCode("code", "newService", "newDescription");
    Optional<Catalog> updatedCatalog = catalogRepository.findCatalogByCodeIgnoreCase("code");
    assertThat(updatedCatalog).isPresent();
    Catalog catalog = updatedCatalog.get();
    assertThat(catalog.getService()).isEqualTo("newService");
    assertThat(catalog.getDescription()).isEqualTo("newDescription");
  }

  @Test
  void given_existing_catalog_findByCode_should_return_catalog() {
    var catalog =
        Catalog.builder().code("code").description("description").service("service").build();
    catalogRepository.save(catalog);

    Optional<Catalog> foundCatalog = catalogRepository.findCatalogByCodeIgnoreCase("code");

    assertThat(foundCatalog).isPresent();
    assertCatalogEquals(catalog, foundCatalog.get());
  }

  @Test
  void find_all_catalogs_should_return_list_of_catalog() {
    var catalog1 =
        Catalog.builder().code("code1").description("description1").service("service1").build();

    var catalog2 =
        Catalog.builder().code("code2").description("description2").service("service2").build();

    catalogRepository.save(catalog1);
    catalogRepository.save(catalog2);

    assertThat(catalogRepository.findAll()).contains(catalog1, catalog2);
  }
}
