package sarl.eazycar.items.domain.repository;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Cart;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.entity.enums.CartStatus;
import sarl.eazycar.items.infrastructure.config.TestDataBaseConfig;

/**
 * Test class for PurchaseRepository
 */
@DataJpaTest
@Import(TestDataBaseConfig.class)
class PurchaseRepositoryTest {

    @Autowired
    private PurchaseRepository purchaseRepository;

    @Autowired
    private ArticleRepository articleRepository;

    private Article createArticle(String articleId) {
        Article article = new Article();
        article.setArticleId(articleId);
        article.setName("Test Article");
        article.setDescription("Test Description");
        return articleRepository.save(article);
    }

    private Cart createCart(String cartId, String accountId) {
        Cart cart = new Cart();
        cart.setCartId(cartId);
        cart.setAccountId(accountId);
        cart.setStatus(CartStatus.ACTIVE);
        cart.setTotalAmount(BigDecimal.ZERO);
        return cart;
    }

    private Purchase createPurchase(LocalDateTime orderDate, OrderStatus status, String currency,
                                   Integer quantity, BigDecimal unitPrice, String articleId,
                                   String accountId, String shippingAddress, LocalDate estimatedDeliveryDate,
                                   BigDecimal totalAmount) {
        Article article = createArticle(articleId);

        Purchase purchase = new Purchase();
        purchase.setOrderDate(orderDate);
        purchase.setStatus(status);
        purchase.setCurrency(currency);
        purchase.setQuantity(quantity);
        purchase.setUnitPrice(unitPrice);
        purchase.setArticle(article);
        purchase.setAccountId(accountId);
        purchase.setShippingAddress(shippingAddress);
        purchase.setEstimatedDeliveryDate(estimatedDeliveryDate);
        purchase.setTotalAmount(totalAmount);
        return purchase;
    }

    @Test
    void save_purchase_successfully() {
        // Given
        Purchase purchase = createPurchase(
                LocalDateTime.now(),
                OrderStatus.PENDING,
                "USD",
                2,
                BigDecimal.valueOf(100.00),
                "article-123",
                "account-456",
                "123 Main Street, City, Country",
                LocalDate.now().plusDays(7),
                BigDecimal.valueOf(200.00)
        );

        // When
        Purchase savedPurchase = purchaseRepository.save(purchase);

        // Then
        assertThat(savedPurchase).isNotNull();
        assertThat(savedPurchase.getOrderId()).isNotNull();
        assertThat(savedPurchase.getStatus()).isEqualTo(OrderStatus.PENDING);
        assertThat(savedPurchase.getShippingAddress()).isEqualTo("123 Main Street, City, Country");
        assertThat(savedPurchase.getEstimatedDeliveryDate()).isEqualTo(LocalDate.now().plusDays(7));
        assertThat(savedPurchase.getTotalAmount()).isEqualByComparingTo(BigDecimal.valueOf(200.00));
    }

    @Test
    void find_purchases_by_account_id() {
        // Given
        String accountId = "account-test-123";
        
        Purchase purchase1 = createPurchase(
                LocalDateTime.now(),
                OrderStatus.PENDING,
                "USD",
                1,
                BigDecimal.valueOf(100.00),
                "article-1",
                accountId,
                "Address 1",
                LocalDate.now().plusDays(5),
                BigDecimal.valueOf(100.00)
        );

        Purchase purchase2 = createPurchase(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "EUR",
                2,
                BigDecimal.valueOf(150.00),
                "article-2",
                accountId,
                "Address 2",
                LocalDate.now().plusDays(10),
                BigDecimal.valueOf(300.00)
        );

        purchaseRepository.save(purchase1);
        purchaseRepository.save(purchase2);

        // When
        List<Purchase> purchases = purchaseRepository.findByAccountId(accountId);

        // Then
        assertThat(purchases).hasSize(2);
        assertThat(purchases).extracting(Purchase::getAccountId)
                .containsOnly(accountId);
    }

    @Test
    void find_purchases_by_status() {
        // Given
        Purchase pendingPurchase = createPurchase(
                LocalDateTime.now(),
                OrderStatus.PENDING,
                "USD",
                1,
                BigDecimal.valueOf(100.00),
                "article-1",
                "account-1",
                "Address 1",
                LocalDate.now().plusDays(5),
                BigDecimal.valueOf(100.00)
        );

        Purchase confirmedPurchase = createPurchase(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "USD",
                1,
                BigDecimal.valueOf(200.00),
                "article-2",
                "account-2",
                "Address 2",
                LocalDate.now().plusDays(7),
                BigDecimal.valueOf(200.00)
        );

        purchaseRepository.save(pendingPurchase);
        purchaseRepository.save(confirmedPurchase);

        // When
        List<Purchase> pendingPurchases = purchaseRepository.findByStatus(OrderStatus.PENDING);
        List<Purchase> confirmedPurchases = purchaseRepository.findByStatus(OrderStatus.CONFIRMED);

        // Then
        assertThat(pendingPurchases).hasSize(1);
        assertThat(pendingPurchases.get(0).getStatus()).isEqualTo(OrderStatus.PENDING);
        
        assertThat(confirmedPurchases).hasSize(1);
        assertThat(confirmedPurchases.get(0).getStatus()).isEqualTo(OrderStatus.CONFIRMED);
    }

    @Test
    void find_purchases_by_estimated_delivery_date_between() {
        // Given
        LocalDate startDate = LocalDate.now().plusDays(5);
        LocalDate endDate = LocalDate.now().plusDays(15);
        
        Purchase purchase1 = createPurchase(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "USD",
                1,
                BigDecimal.valueOf(100.00),
                "article-1",
                "account-1",
                "Address 1",
                LocalDate.now().plusDays(7), // Within range
                BigDecimal.valueOf(100.00)
        );

        Purchase purchase2 = createPurchase(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "USD",
                1,
                BigDecimal.valueOf(200.00),
                "article-2",
                "account-2",
                "Address 2",
                LocalDate.now().plusDays(20), // Outside range
                BigDecimal.valueOf(200.00)
        );

        Purchase purchase3 = createPurchase(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "USD",
                1,
                BigDecimal.valueOf(150.00),
                "article-3",
                "account-3",
                "Address 3",
                LocalDate.now().plusDays(10), // Within range
                BigDecimal.valueOf(150.00)
        );

        purchaseRepository.save(purchase1);
        purchaseRepository.save(purchase2);
        purchaseRepository.save(purchase3);

        // When
        List<Purchase> purchasesInRange = purchaseRepository.findByEstimatedDeliveryDateBetween(startDate, endDate);

        // Then
        assertThat(purchasesInRange).hasSize(2);
        assertThat(purchasesInRange).extracting(Purchase::getEstimatedDeliveryDate)
                .allMatch(date -> !date.isBefore(startDate) && !date.isAfter(endDate));
    }

    @Test
    void find_purchases_by_account_id_and_status() {
        // Given
        String accountId = "account-test-456";
        
        Purchase pendingPurchase = createPurchase(
                LocalDateTime.now(),
                OrderStatus.PENDING,
                "USD",
                1,
                BigDecimal.valueOf(100.00),
                "article-1",
                accountId,
                "Address 1",
                LocalDate.now().plusDays(5),
                BigDecimal.valueOf(100.00)
        );

        Purchase confirmedPurchase = createPurchase(
                LocalDateTime.now(),
                OrderStatus.CONFIRMED,
                "USD",
                1,
                BigDecimal.valueOf(200.00),
                "article-2",
                accountId,
                "Address 2",
                LocalDate.now().plusDays(7),
                BigDecimal.valueOf(200.00)
        );

        Purchase otherAccountPurchase = createPurchase(
                LocalDateTime.now(),
                OrderStatus.PENDING,
                "USD",
                1,
                BigDecimal.valueOf(150.00),
                "article-3",
                "other-account",
                "Address 3",
                LocalDate.now().plusDays(6),
                BigDecimal.valueOf(150.00)
        );

        purchaseRepository.save(pendingPurchase);
        purchaseRepository.save(confirmedPurchase);
        purchaseRepository.save(otherAccountPurchase);

        // When
        List<Purchase> pendingPurchasesForAccount = purchaseRepository.findByAccountIdAndStatus(accountId, OrderStatus.PENDING);

        // Then
        assertThat(pendingPurchasesForAccount).hasSize(1);
        assertThat(pendingPurchasesForAccount.get(0).getAccountId()).isEqualTo(accountId);
        assertThat(pendingPurchasesForAccount.get(0).getStatus()).isEqualTo(OrderStatus.PENDING);
    }

    @Test
    void update_purchase_successfully() {
        // Given
        Purchase purchase = createPurchase(
                LocalDateTime.now(),
                OrderStatus.PENDING,
                "USD",
                1,
                BigDecimal.valueOf(100.00),
                "article-123",
                "account-456",
                "Original Address",
                LocalDate.now().plusDays(7),
                BigDecimal.valueOf(100.00)
        );

        Purchase savedPurchase = purchaseRepository.save(purchase);

        // When
        savedPurchase.setStatus(OrderStatus.CONFIRMED);
        savedPurchase.setShippingAddress("Updated Address");
        savedPurchase.setEstimatedDeliveryDate(LocalDate.now().plusDays(5));
        Purchase updatedPurchase = purchaseRepository.save(savedPurchase);

        // Then
        assertThat(updatedPurchase.getOrderId()).isEqualTo(savedPurchase.getOrderId());
        assertThat(updatedPurchase.getStatus()).isEqualTo(OrderStatus.CONFIRMED);
        assertThat(updatedPurchase.getShippingAddress()).isEqualTo("Updated Address");
        assertThat(updatedPurchase.getEstimatedDeliveryDate()).isEqualTo(LocalDate.now().plusDays(5));
    }
}
