package sarl.eazycar.items.domain.repository;

import static org.assertj.core.api.Assertions.assertThat;
import static sarl.eazycar.items.domain.repository.Assertions.assertArticleOptionEquals;

import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import sarl.eazycar.items.domain.entity.ArticleOption;
import sarl.eazycar.items.infrastructure.config.TestDataBaseConfig;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@DataJpaTest
@Import(TestDataBaseConfig.class)
class ArticleOptionRepositoryTest {

  @Autowired private ArticleOptionRepository articleOptionRepository;

  @Test
  void save_article_option_object_should_return_article_option_entity() {
    var articleOption =
        ArticleOption.builder()
            .optionName("optionName")
            .optionDescription("optionDescription")
            .build();

    ArticleOption savedArticleOption = articleOptionRepository.save(articleOption);

    assertArticleOptionEquals(articleOption, savedArticleOption);
  }

  @Test
  void update_article_option_object_by_option_name_should_return_article_option_entity() {
    articleOptionRepository.save(
        ArticleOption.builder()
            .optionName("optionName")
            .optionDescription("optionDescription")
            .build());
    articleOptionRepository.updateArticleOptionByOptionName("optionName", "newOptionDescription");
    Optional<ArticleOption> updatedArticleOptionOptional =
        articleOptionRepository.findArticleOptionByOptionNameIgnoreCase("optionName");

    assertThat(updatedArticleOptionOptional).isPresent();
    ArticleOption updatedArticleOption = updatedArticleOptionOptional.get();
    assertThat(updatedArticleOption.getOptionDescription()).isEqualTo("newOptionDescription");
  }

  @Test
  void find_article_option_by_option_name_should_return_article_option() {
    var articleOption =
        ArticleOption.builder()
            .optionName("optionName")
            .optionDescription("optionDescription")
            .build();

    articleOptionRepository.save(articleOption);

    Optional<ArticleOption> foundArticleOption =
        articleOptionRepository.findArticleOptionByOptionNameIgnoreCase("optionName");

    assertThat(foundArticleOption).isPresent();
    assertArticleOptionEquals(articleOption, foundArticleOption.get());
  }

  @Test
  void find_all_article_options_should_return_list_of_article_option() {
    var articleOption1 =
        ArticleOption.builder()
            .optionName("optionName1")
            .optionDescription("optionDescription1")
            .build();

    var articleOption2 =
        ArticleOption.builder()
            .optionName("optionName2")
            .optionDescription("optionDescription2")
            .build();

    articleOptionRepository.save(articleOption1);
    articleOptionRepository.save(articleOption2);

    assertThat(articleOptionRepository.findAll()).contains(articleOption1, articleOption2);
  }
}
