package sarl.eazycar.items.domain.repository;

import static java.math.BigDecimal.valueOf;
import static org.assertj.core.api.Assertions.assertThat;
import static sarl.eazycar.items.domain.repository.Assertions.assertArticleOptionEquals;
import static sarl.eazycar.items.domain.repository.Assertions.assertCatalogEquals;
import static sarl.eazycar.items.domain.repository.Assertions.assertCategoryEquals;
import static sarl.eazycar.items.domain.repository.Assertions.assertImageEquals;
import static sarl.eazycar.items.domain.repository.Assertions.assertPriceEquals;
import static sarl.eazycar.items.domain.repository.Assertions.assertVideoEquals;

import java.math.BigDecimal;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.ArticleOption;
import sarl.eazycar.items.domain.entity.Car;
import sarl.eazycar.items.domain.entity.Catalog;
import sarl.eazycar.items.domain.entity.Category;
import sarl.eazycar.items.domain.entity.Image;
import sarl.eazycar.items.domain.entity.Price;
import sarl.eazycar.items.domain.entity.Video;
import sarl.eazycar.items.infrastructure.config.TestDataBaseConfig;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
@DataJpaTest
@Import(TestDataBaseConfig.class)
class PriceRepositoryTest {

  @Autowired private PriceRepository priceRepository;

  @Autowired private CarRepository carRepository;

  @Autowired private ArticleOptionRepository articleOptionRepository;

  @Autowired private CatalogRepository catalogRepository;

  @Autowired private CategoryRepository categoryRepository;

  @Test
  void get_list_of_article_price_with_image_or_video_by_article_option_category_and_catalog() {
    // Create and save Category
    var category =
        Category.builder()
            .type("type")
            .description("description")
            .label("label")
            .minPrice(valueOf(1000))
            .maxPrice(valueOf(2000))
            .build();
    categoryRepository.save(category);

    // Create and save Catalog
    var catalog =
        Catalog.builder().code("code").description("description").service("service").build();
    catalogRepository.save(catalog);

    // Create and save ArticleOption
    var articleOption =
        ArticleOption.builder()
            .optionName("optionName")
            .optionDescription("optionDescription")
            .build();
    articleOptionRepository.save(articleOption);

    // Create and save Image
    Image image =
        Image.builder().name("Image Name").path("/path/to/image").format("jpg").size(1024).build();

    // Create and save Video
    Video video =
        Video.builder().name("Video Name").path("/path/to/video").format("mp4").size(2048).build();

    // Create and save Car(Article)
    Article article =
        Article.builder()
            .name("Article name")
            .description("Article description")
            .cityLocation("City location")
            .districtLocation("District location")
            .available(true)
            .visible(true)
            .category(category)
            .images(List.of(image))
            .videos(List.of(video))
            .build();
    Car car =
        Car.builder().brand("brand").model("model").factoryYear(2021).article(article).build();
    carRepository.save(car);

    // Create and save Price
    Price price =
        Price.builder()
            .price(BigDecimal.valueOf(1500))
            .currency("USD")
            .discountPercent(BigDecimal.valueOf(10))
            .catalog(catalog)
            .articleOption(articleOption)
            .article(article)
            .build();
    priceRepository.save(price);

    // Fetch the list of article prices with image or video by article option, category and catalog
    List<Price> prices =
        priceRepository.findByArticleOptionAndArticleCategoryAndCatalog(
            articleOption.getOptionName(), category.getType(), catalog.getCode());

    // Assertions
    assertThat(prices).isNotEmpty();
    assertThat(prices).hasSize(1);
    assertPriceEquals(price, prices.get(0));
    assertImageEquals(image, prices.get(0).getArticle().getImages().get(0));
    assertVideoEquals(video, prices.get(0).getArticle().getVideos().get(0));
    assertCatalogEquals(catalog, prices.get(0).getCatalog());
    assertArticleOptionEquals(articleOption, prices.get(0).getArticleOption());
    assertCategoryEquals(category, prices.get(0).getArticle().getCategory());
  }

  @Test
  void get_list_of_article_price_with_image_or_video_by_article_option_and_catalog() {
    // Create and save Catalog
    var catalog =
        Catalog.builder().code("code").description("description").service("service").build();
    catalogRepository.save(catalog);

    // Create and save ArticleOption
    var articleOption =
        ArticleOption.builder()
            .optionName("optionName")
            .optionDescription("optionDescription")
            .build();
    articleOptionRepository.save(articleOption);

    // Create and save Image
    Image image =
        Image.builder().name("Image Name").path("/path/to/image").format("jpg").size(1024).build();

    // Create and save Video
    Video video =
        Video.builder().name("Video Name").path("/path/to/video").format("mp4").size(2048).build();

    // Create and save Car(Article)
    Article article =
        Article.builder()
            .name("Article name")
            .description("Article description")
            .cityLocation("City location")
            .districtLocation("District location")
            .available(true)
            .visible(true)
            .images(List.of(image))
            .videos(List.of(video))
            .build();
    Car car =
        Car.builder().brand("brand").model("model").factoryYear(2021).article(article).build();
    carRepository.save(car);

    // Create and save Price
    Price price =
        Price.builder()
            .price(BigDecimal.valueOf(1500))
            .currency("USD")
            .discountPercent(BigDecimal.valueOf(10))
            .catalog(catalog)
            .articleOption(articleOption)
            .article(article)
            .build();
    priceRepository.save(price);

    // Fetch the list of article prices with image or video by article option, category and catalog
    List<Price> prices =
        priceRepository.findByArticleOptionAndCatalog(
            articleOption.getOptionName(), catalog.getCode());

    // Assertions
    assertThat(prices).isNotEmpty();
    assertThat(prices).hasSize(1);
    assertPriceEquals(price, prices.get(0));
    assertImageEquals(image, prices.get(0).getArticle().getImages().get(0));
    assertVideoEquals(video, prices.get(0).getArticle().getVideos().get(0));
    assertCatalogEquals(catalog, prices.get(0).getCatalog());
    assertArticleOptionEquals(articleOption, prices.get(0).getArticleOption());
  }
}
