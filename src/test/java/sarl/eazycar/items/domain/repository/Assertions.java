package sarl.eazycar.items.domain.repository;

import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.ArticleOption;
import sarl.eazycar.items.domain.entity.Car;
import sarl.eazycar.items.domain.entity.Catalog;
import sarl.eazycar.items.domain.entity.Category;
import sarl.eazycar.items.domain.entity.Image;
import sarl.eazycar.items.domain.entity.Price;
import sarl.eazycar.items.domain.entity.Video;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */

public class Assertions {

	static void assertArticleOptionEquals(ArticleOption expected, ArticleOption actual) {
		assertThat(actual).isNotNull();
		assertThat(actual.getOptionName()).isEqualTo(expected.getOptionName());
		assertThat(actual.getOptionDescription()).isEqualTo(expected.getOptionDescription());
	}

	static void assertCatalogEquals(Catalog expected, Catalog actual) {
		assertThat(actual).isNotNull();
		assertThat(actual.getCode()).isEqualTo(expected.getCode());
		assertThat(actual.getService()).isEqualTo(expected.getService());
		assertThat(actual.getDescription()).isEqualTo(expected.getDescription());
	}

	static void assertCategoryEquals(Category expected, Category actual) {
		assertThat(actual).isNotNull();
		assertThat(actual.getType()).isEqualTo(expected.getType());
		assertThat(actual.getLabel()).isEqualTo(expected.getLabel());
		assertThat(actual.getDescription()).isEqualTo(expected.getDescription());
		assertThat(actual.getMinPrice()).isEqualTo(expected.getMinPrice());
		assertThat(actual.getMaxPrice()).isEqualTo(expected.getMaxPrice());
	}

	static void assertArticleEquals(Article expected, Article actual) {
		assertThat(actual).isNotNull();
		assertThat(actual.getArticleId()).isEqualTo(expected.getArticleId());
		assertThat(actual.getName()).isEqualTo(expected.getName());
		assertThat(actual.getDescription()).isEqualTo(expected.getDescription());
		assertThat(actual.getCityLocation()).isEqualTo(expected.getCityLocation());
		assertThat(actual.getDistrictLocation()).isEqualTo(expected.getDistrictLocation());
		assertThat(actual.isAvailable()).isEqualTo(expected.isAvailable());
		assertThat(actual.isVisible()).isEqualTo(expected.isVisible());
		
		if (!expected.getVideos().isEmpty() && !actual.getVideos().isEmpty()) {
			assertVideoEquals(expected.getVideos().get(0), actual.getVideos().get(0));
		}
		
		if (!expected.getImages().isEmpty() && !actual.getImages().isEmpty()) {
			assertImageEquals(expected.getImages().get(0), actual.getImages().get(0));
		}
		
		if (!expected.getPrices().isEmpty() && !actual.getPrices().isEmpty()) {
			assertPriceEquals(expected.getPrices().get(0), actual.getPrices().get(0));
		}
	}

	static void assertCarEquals(Car actual, Car expected) {
		assertThat(actual).isNotNull();
		assertThat(actual.getCarId()).isEqualTo(expected.getCarId());
		assertThat(actual.getBrand()).isEqualTo(expected.getBrand());
		assertThat(actual.getModel()).isEqualTo(expected.getModel());
		assertThat(actual.getFactoryYear()).isEqualTo(expected.getFactoryYear());
		assertArticleEquals(actual.getArticle(), expected.getArticle());

	}

	static void assertVideoEquals(Video actual, Video expected) {
		assertThat(actual).isNotNull();
		assertThat(actual.getFileId()).isEqualTo(expected.getFileId());
		assertThat(actual.getName()).isEqualTo(expected.getName());
		assertThat(actual.getPath()).isEqualTo(expected.getPath());
		assertThat(actual.getSize()).isEqualTo(expected.getSize());
		assertThat(actual.getFormat()).isEqualTo(expected.getFormat());
	}

	static void assertImageEquals(Image actual, Image expected) {
		assertThat(actual).isNotNull();
		assertThat(actual.getFileId()).isEqualTo(expected.getFileId());
		assertThat(actual.getName()).isEqualTo(expected.getName());
		assertThat(actual.getPath()).isEqualTo(expected.getPath());
		assertThat(actual.getSize()).isEqualTo(expected.getSize());
		assertThat(actual.getFormat()).isEqualTo(expected.getFormat());
	}

	static void assertPriceEquals(Price actual, Price expected) {
		assertThat(actual).isNotNull();
		assertThat(actual.getPriceId()).isEqualTo(expected.getPriceId());
		assertThat(actual.getPrice()).isEqualTo(expected.getPrice());
		assertThat(actual.getCurrency()).isEqualTo(expected.getCurrency());
		assertThat(actual.getDiscountPercent()).isEqualTo(expected.getDiscountPercent());
	}


}
