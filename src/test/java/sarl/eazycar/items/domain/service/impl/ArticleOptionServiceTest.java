package sarl.eazycar.items.domain.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.ArticleOption;
import sarl.eazycar.items.domain.repository.ArticleOptionRepository;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
class ArticleOptionServiceTest {

  @Mock private ArticleOptionRepository articleOptionRepository;

  @InjectMocks private ArticleOptionService articleOptionService;

  private ArticleOption articleOption;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    articleOption =
        ArticleOption.builder()
            .optionName("Test Option")
            .optionDescription("Test Description")
            .build();
  }

  // Happy Path Tests

  @Test
  void givenValidArticleOption_whenCreateArticleOption_thenReturnsCreatedArticleOption() {
    // given
    when(articleOptionRepository.findArticleOptionByOptionNameIgnoreCase(anyString()))
        .thenReturn(Optional.empty());
    when(articleOptionRepository.save(any(ArticleOption.class))).thenReturn(articleOption);

    // when
    ArticleOption result = articleOptionService.createArticleOption(articleOption);

    // then
    assertNotNull(result);
    assertEquals("Test Option", result.getOptionName());
    assertEquals("Test Description", result.getOptionDescription());
    verify(articleOptionRepository, times(1))
        .findArticleOptionByOptionNameIgnoreCase(articleOption.getOptionName());
    verify(articleOptionRepository, times(1)).save(articleOption);
  }

  @Test
  void givenExistingName_whenCreateArticleOption_thenThrowsFunctionalErrorException() {
    // given
    when(articleOptionRepository.findArticleOptionByOptionNameIgnoreCase(anyString()))
        .thenReturn(Optional.of(articleOption));

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class,
            () -> {
              articleOptionService.createArticleOption(articleOption);
            });

    assertTrue(exception.getMessage().contains("already exists"));
    verify(articleOptionRepository, times(1))
        .findArticleOptionByOptionNameIgnoreCase(articleOption.getOptionName());
    verify(articleOptionRepository, never()).save(any(ArticleOption.class));
  }

  @Test
  void givenArticleOptionWithId_whenCreateArticleOption_thenThrowsFunctionalErrorException() {
    // given
    articleOption.setOptionId("123");

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class,
            () -> {
              articleOptionService.createArticleOption(articleOption);
            });

    assertEquals("A new articleOption cannot already have an ID.", exception.getMessage());
    verify(articleOptionRepository, never()).findArticleOptionByOptionNameIgnoreCase(anyString());
    verify(articleOptionRepository, never()).save(any(ArticleOption.class));
  }

  @Test
  void givenValidId_whenGetArticleOptionById_thenReturnsArticleOption() {
    // given
    String optionId = "123";
    articleOption.setOptionId(optionId);
    when(articleOptionRepository.findById(optionId)).thenReturn(Optional.of(articleOption));

    // when
    ArticleOption result = articleOptionService.getArticleOptionById(optionId);

    // then
    assertNotNull(result);
    assertEquals(optionId, result.getOptionId());
    assertEquals("Test Option", result.getOptionName());
    verify(articleOptionRepository, times(1)).findById(optionId);
  }

  @Test
  void givenValidName_whenGetArticleOptionByName_thenReturnsArticleOption() {
    // given
    String optionName = "Test Option";
    when(articleOptionRepository.findArticleOptionByOptionNameIgnoreCase(optionName))
        .thenReturn(Optional.of(articleOption));

    // when
    ArticleOption result = articleOptionService.getArticleOptionByName(optionName);

    // then
    assertNotNull(result);
    assertEquals(optionName, result.getOptionName());
    verify(articleOptionRepository, times(1)).findArticleOptionByOptionNameIgnoreCase(optionName);
  }

  @Test
  void givenNonExistentId_whenGetArticleOptionById_thenThrowsFunctionalErrorException() {
    // given
    String optionId = "non-existent";
    when(articleOptionRepository.findById(optionId)).thenReturn(Optional.empty());

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class,
            () -> {
              articleOptionService.getArticleOptionById(optionId);
            });

    assertTrue(exception.getMessage().contains("not found"));
    verify(articleOptionRepository, times(1)).findById(optionId);
  }

  @Test
  void givenNonExistentName_whenGetArticleOptionByName_thenThrowsFunctionalErrorException() {
    // given
    String optionName = "non-existent";
    when(articleOptionRepository.findArticleOptionByOptionNameIgnoreCase(optionName))
        .thenReturn(Optional.empty());

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class,
            () -> {
              articleOptionService.getArticleOptionByName(optionName);
            });

    assertTrue(exception.getMessage().contains("not found"));
    verify(articleOptionRepository, times(1)).findArticleOptionByOptionNameIgnoreCase(optionName);
  }

  @Test
  void givenValidArticleOption_whenUpdateArticleOption_thenReturnsUpdatedArticleOption() {
    // given
    String optionId = "123";
    ArticleOption existingOption =
        ArticleOption.builder()
            .optionId(optionId)
            .optionName("Existing Option")
            .optionDescription("Existing Description")
            .build();

    ArticleOption updatedOption =
        ArticleOption.builder()
            .optionName("Updated Option")
            .optionDescription("Updated Description")
            .build();

    when(articleOptionRepository.findById(optionId)).thenReturn(Optional.of(existingOption));
    when(articleOptionRepository.findArticleOptionByOptionNameIgnoreCase("Updated Option"))
        .thenReturn(Optional.empty());
    when(articleOptionRepository.save(any(ArticleOption.class))).thenReturn(existingOption);

    // when
    ArticleOption result = articleOptionService.updateArticleOption(optionId, updatedOption);

    // then
    assertNotNull(result);
    assertEquals(optionId, result.getOptionId());
    assertEquals("Updated Option", result.getOptionName());
    assertEquals("Updated Description", result.getOptionDescription());
    verify(articleOptionRepository, times(1)).findById(optionId);
    verify(articleOptionRepository, times(1))
        .findArticleOptionByOptionNameIgnoreCase("Updated Option");
    verify(articleOptionRepository, times(1)).save(existingOption);
  }

  @Test
  void givenBlankId_whenUpdateArticleOption_thenThrowsFunctionalErrorException() {
    // given
    String optionId = "";
    ArticleOption updatedOption =
        ArticleOption.builder()
            .optionName("Updated Option")
            .optionDescription("Updated Description")
            .build();

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class,
            () -> {
              articleOptionService.updateArticleOption(optionId, updatedOption);
            });

    assertEquals("Invalid id", exception.getMessage());
    verify(articleOptionRepository, never()).findById(anyString());
    verify(articleOptionRepository, never()).save(any(ArticleOption.class));
  }

  @Test
  void givenExistingName_whenUpdateArticleOption_thenThrowsFunctionalErrorException() {
    // given
    String optionId = "123";
    ArticleOption existingOption =
        ArticleOption.builder()
            .optionId(optionId)
            .optionName("Existing Option")
            .optionDescription("Existing Description")
            .build();

    ArticleOption updatedOption =
        ArticleOption.builder()
            .optionName("Updated Option")
            .optionDescription("Updated Description")
            .build();

    ArticleOption conflictingOption =
        ArticleOption.builder()
            .optionId("456")
            .optionName("Updated Option")
            .optionDescription("Another Description")
            .build();

    when(articleOptionRepository.findById(optionId)).thenReturn(Optional.of(existingOption));
    when(articleOptionRepository.findArticleOptionByOptionNameIgnoreCase("Updated Option"))
        .thenReturn(Optional.of(conflictingOption));

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class,
            () -> {
              articleOptionService.updateArticleOption(optionId, updatedOption);
            });

    assertTrue(exception.getMessage().contains("already exists"));
    verify(articleOptionRepository, times(1)).findById(optionId);
    verify(articleOptionRepository, times(1))
        .findArticleOptionByOptionNameIgnoreCase("Updated Option");
    verify(articleOptionRepository, never()).save(any(ArticleOption.class));
  }

  @Test
  void whenGetAllArticleOptions_thenReturnsAllArticleOptions() {
    // given
    List<ArticleOption> articleOptions = new ArrayList<>();
    articleOptions.add(ArticleOption.builder().optionId("1").optionName("Option 1").build());
    articleOptions.add(ArticleOption.builder().optionId("2").optionName("Option 2").build());
    when(articleOptionRepository.findAll()).thenReturn(articleOptions);

    // when
    List<ArticleOption> result = articleOptionService.getAllArticleOptions();

    // then
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("Option 1", result.get(0).getOptionName());
    assertEquals("Option 2", result.get(1).getOptionName());
    verify(articleOptionRepository, times(1)).findAll();
  }
}
