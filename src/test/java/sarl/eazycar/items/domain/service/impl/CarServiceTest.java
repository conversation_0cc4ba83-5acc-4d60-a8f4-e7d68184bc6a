package sarl.eazycar.items.domain.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import sarl.eazycar.items.application.dto.criteria.CarSearchCriteria;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.ArticleOption;
import sarl.eazycar.items.domain.entity.Car;
import sarl.eazycar.items.domain.entity.Catalog;
import sarl.eazycar.items.domain.entity.Category;
import sarl.eazycar.items.domain.entity.Price;
import sarl.eazycar.items.domain.repository.CarRepository;
import sarl.eazycar.items.domain.service.IArticleOptionService;
import sarl.eazycar.items.domain.service.ICatalogService;
import sarl.eazycar.items.domain.service.ICategoryService;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
class CarServiceTest {

  @Mock private CarRepository carRepository;

  @Mock private IArticleOptionService articleOptionService;

  @Mock private ICatalogService catalogService;

  @Mock private ICategoryService categoryService;

  @InjectMocks private CarService carService;

  private Car car;
  private Article article;
  private Category category;
  private Price price;
  private ArticleOption articleOption;
  private Catalog catalog;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);

    // Setup test data
    category =
        Category.builder()
            .categoryId("cat123")
            .type("Sedan")
            .label("Luxury Sedan")
            .minPrice(new BigDecimal("100.00"))
            .maxPrice(new BigDecimal("1000.00"))
            .build();

    articleOption = ArticleOption.builder().optionId("opt123").optionName("Daily Rental").build();

    catalog = Catalog.builder().catalogId("cat123").service("Standard Catalog").build();

    price =
        Price.builder()
            .priceId("price123")
            .price(new BigDecimal("500.00"))
            .currency("USD")
            .discountPercent(new BigDecimal("0.00"))
            .articleOption(articleOption)
            .catalog(catalog)
            .build();

    article =
        Article.builder()
            .articleId("art123")
            .name("Luxury Car")
            .description("A luxury car for rent")
            .cityLocation("New York")
            .districtLocation("Manhattan")
            .available(true)
            .visible(true)
            .category(category)
            .prices(new ArrayList<>(Collections.singletonList(price)))
            .build();

    car =
        Car.builder().brand("Mercedes").model("S-Class").factoryYear(2023).article(article).build();

    // Setup common mocks
    when(categoryService.getCategoryById(anyString())).thenReturn(category);
    when(articleOptionService.getArticleOptionById(anyString())).thenReturn(articleOption);
    when(catalogService.getCatalogById(anyString())).thenReturn(catalog);
  }

  // Existing tests

  @Test
  void givenCar_whenCreateCar_thenReturnsCar() {
    // given
    when(carRepository.save(any(Car.class))).thenReturn(car);

    // when
    Car result = carService.createCar(car);

    // then
    assertNotNull(result);
    assertEquals("Mercedes", result.getBrand());
    assertEquals("S-Class", result.getModel());
    verify(carRepository, times(1)).save(any(Car.class));
    verify(categoryService, times(1)).getCategoryById(anyString());
  }

  @Test
  void givenCar_whenFindCarById_thenReturnsCar() {
    // given
    String carId = "car123";
    when(carRepository.findById(carId)).thenReturn(Optional.of(car));

    // when
    Car result = carService.getCarById(carId);

    // then
    assertNotNull(result);
    assertEquals("Mercedes", result.getBrand());
    verify(carRepository, times(1)).findById(carId);
  }

  // New tests based on selected scenarios

  @Test
  void givenValidData_whenUpdateCar_thenReturnsUpdatedCar() {
    // given
    String carId = "car123";
    Car existingCar =
        Car.builder()
            .carId(carId)
            .brand("BMW")
            .model("3-Series")
            .factoryYear(2020)
            .article(article)
            .build();

    Car updatedCarData =
        Car.builder()
            .brand("BMW")
            .model("5-Series")
            .factoryYear(2022)
            .article(
                Article.builder()
                    .name("Updated Luxury Car")
                    .description("An updated luxury car for rent")
                    .cityLocation("Los Angeles")
                    .districtLocation("Beverly Hills")
                    .available(true)
                    .visible(true)
                    .category(category)
                    .prices(new ArrayList<>(Collections.singletonList(price)))
                    .build())
            .build();

    when(carRepository.findById(carId)).thenReturn(Optional.of(existingCar));
    when(carRepository.save(any(Car.class))).thenReturn(existingCar);

    // when
    Car result = carService.updateCar(carId, updatedCarData);

    // then
    assertNotNull(result);
    assertEquals(carId, result.getCarId());
    assertEquals("BMW", result.getBrand());
    assertEquals("5-Series", result.getModel());
    assertEquals(2022, result.getFactoryYear());
    assertEquals("Updated Luxury Car", result.getArticle().getName());
    assertEquals("Los Angeles", result.getArticle().getCityLocation());
    verify(carRepository, times(1)).findById(carId);
    verify(carRepository, times(1)).save(existingCar);
  }

  @Test
  void whenGetAllCars_thenReturnsAllCars() {
    // given
    List<Car> cars =
        Arrays.asList(
            Car.builder().carId("car1").brand("BMW").model("3-Series").build(),
            Car.builder().carId("car2").brand("Audi").model("A4").build());
    when(carRepository.findAll()).thenReturn(cars);

    // when
    List<Car> result = carService.getAllCars();

    // then
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("BMW", result.get(0).getBrand());
    assertEquals("Audi", result.get(1).getBrand());
    verify(carRepository, times(1)).findAll();
  }

  @Test
  void givenCarWithInvalidCategory_whenCreateCar_thenThrowsException() {
    // given
    Car carWithoutCategory =
        Car.builder()
            .brand("Mercedes")
            .model("S-Class")
            .factoryYear(2023)
            .article(
                Article.builder().name("Luxury Car").description("A luxury car for rent").build())
            .build();

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> carService.createCar(carWithoutCategory));

    assertEquals("Article must have a category", exception.getMessage());
    verify(carRepository, never()).save(any(Car.class));
  }

  @Test
  void givenCarWithInvalidPrice_whenCreateCar_thenThrowsException() {
    // given
    Price invalidPrice =
        Price.builder()
            .price(new BigDecimal("2000.00")) // Above max price
            .currency("USD")
            .articleOption(articleOption)
            .catalog(catalog)
            .build();

    Article articleWithInvalidPrice =
        Article.builder()
            .name("Luxury Car")
            .description("A luxury car for rent")
            .category(category)
            .prices(new ArrayList<>(Collections.singletonList(invalidPrice)))
            .build();

    Car carWithInvalidPrice =
        Car.builder()
            .brand("Mercedes")
            .model("S-Class")
            .factoryYear(2023)
            .article(articleWithInvalidPrice)
            .build();

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> carService.createCar(carWithInvalidPrice));

    assertTrue(exception.getMessage().contains("The price must be between the min price"));
    verify(carRepository, never()).save(any(Car.class));
  }

  @Test
  void givenInvalidId_whenUpdateCar_thenThrowsException() {
    // given
    String invalidId = "";

    // when & then
    FunctionalErrorException exception =
        assertThrows(FunctionalErrorException.class, () -> carService.updateCar(invalidId, car));

    assertEquals("Invalid id", exception.getMessage());
    verify(carRepository, never()).findById(anyString());
    verify(carRepository, never()).save(any(Car.class));
  }

  @Test
  void givenNonExistentId_whenUpdateCar_thenThrowsException() {
    // given
    String nonExistentId = "non-existent";
    when(carRepository.findById(nonExistentId))
        .thenThrow(new FunctionalErrorException("Car with id " + nonExistentId + " not found"));

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> carService.updateCar(nonExistentId, car));

    assertTrue(exception.getMessage().contains("not found"));
    verify(carRepository, times(1)).findById(nonExistentId);
    verify(carRepository, never()).save(any(Car.class));
  }

  @Test
  void givenCarWithoutArticle_whenCreateCar_thenThrowsException() {
    // given
    Car carWithoutArticle =
        Car.builder().brand("Mercedes").model("S-Class").factoryYear(2023).build();

    // when & then
    FunctionalErrorException exception =
        assertThrows(FunctionalErrorException.class, () -> carService.createCar(carWithoutArticle));

    assertEquals("Car must have an article", exception.getMessage());
    verify(carRepository, never()).save(any(Car.class));
  }

  // Tests for searchCars method

  @SuppressWarnings("unchecked")
  @Test
  void givenBasicCriteria_whenSearchCars_thenReturnsMatchingCars() {
    // given
    CarSearchCriteria criteria = CarSearchCriteria.builder().carBrand("Mercedes").build();

    Pageable pageable = PageRequest.of(0, 10);
    List<Car> cars = Collections.singletonList(car);
    Page<Car> carPage = new PageImpl<>(cars, pageable, cars.size());

    when(carRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(carPage);

    // when
    Page<Car> result = carService.searchCars(criteria, pageable);

    // then
    assertNotNull(result);
    assertEquals(1, result.getTotalElements());
    assertEquals("Mercedes", result.getContent().get(0).getBrand());
    verify(carRepository, times(1)).findAll(any(Specification.class), eq(pageable));
  }

  @SuppressWarnings("unchecked")
  @Test
  void givenMultipleFilters_whenSearchCars_thenReturnsMatchingCars() {
    // given
    CarSearchCriteria criteria =
        CarSearchCriteria.builder()
            .carBrand("Mercedes")
            .carModel("S-Class")
            .locationCity("New York")
            .categoryType("Sedan")
            .build();

    Pageable pageable = PageRequest.of(0, 10);
    List<Car> cars = Collections.singletonList(car);
    Page<Car> carPage = new PageImpl<>(cars, pageable, cars.size());

    when(carRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(carPage);

    // when
    Page<Car> result = carService.searchCars(criteria, pageable);

    // then
    assertNotNull(result);
    assertEquals(1, result.getTotalElements());
    assertEquals("Mercedes", result.getContent().get(0).getBrand());
    assertEquals("S-Class", result.getContent().get(0).getModel());
    assertEquals("New York", result.getContent().get(0).getArticle().getCityLocation());
    verify(carRepository, times(1)).findAll(any(Specification.class), eq(pageable));
  }

  @SuppressWarnings("unchecked")
  @Test
  void givenPriceRange_whenSearchCars_thenReturnsMatchingCars() {
    // given
    CarSearchCriteria criteria =
        CarSearchCriteria.builder()
            .minPrice(new BigDecimal("300.00"))
            .maxPrice(new BigDecimal("700.00"))
            .currency("USD")
            .build();

    Pageable pageable = PageRequest.of(0, 10);
    List<Car> cars = Collections.singletonList(car);
    Page<Car> carPage = new PageImpl<>(cars, pageable, cars.size());

    when(carRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(carPage);

    // when
    Page<Car> result = carService.searchCars(criteria, pageable);

    // then
    assertNotNull(result);
    assertEquals(1, result.getTotalElements());
    assertEquals(
        new BigDecimal("500.00"),
        result.getContent().get(0).getArticle().getPrices().get(0).getPrice());
    assertEquals("USD", result.getContent().get(0).getArticle().getPrices().get(0).getCurrency());
    verify(carRepository, times(1)).findAll(any(Specification.class), eq(pageable));
  }

  @SuppressWarnings("unchecked")
  @Test
  void givenArticleOptions_whenSearchCars_thenReturnsMatchingCars() {
    // given
    CarSearchCriteria criteria =
        CarSearchCriteria.builder()
            .articleOptions(Collections.singletonList("Daily Rental"))
            .build();

    Pageable pageable = PageRequest.of(0, 10);
    List<Car> cars = Collections.singletonList(car);
    Page<Car> carPage = new PageImpl<>(cars, pageable, cars.size());

    when(carRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(carPage);

    // when
    Page<Car> result = carService.searchCars(criteria, pageable);

    // then
    assertNotNull(result);
    assertEquals(1, result.getTotalElements());
    assertEquals(
        "Daily Rental",
        result
            .getContent()
            .get(0)
            .getArticle()
            .getPrices()
            .get(0)
            .getArticleOption()
            .getOptionName());
    verify(carRepository, times(1)).findAll(any(Specification.class), eq(pageable));
  }

  @SuppressWarnings("unchecked")
  @Test
  void givenEmptyCriteria_whenSearchCars_thenReturnsAllCars() {
    // given
    CarSearchCriteria criteria = new CarSearchCriteria();

    Pageable pageable = PageRequest.of(0, 10);
    List<Car> cars =
        Arrays.asList(
            car,
            Car.builder()
                .brand("BMW")
                .model("3-Series")
                .factoryYear(2022)
                .article(article)
                .build());
    Page<Car> carPage = new PageImpl<>(cars, pageable, cars.size());

    when(carRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(carPage);

    // when
    Page<Car> result = carService.searchCars(criteria, pageable);

    // then
    assertNotNull(result);
    assertEquals(2, result.getTotalElements());
    verify(carRepository, times(1)).findAll(any(Specification.class), eq(pageable));
  }

  @SuppressWarnings("unchecked")
  @Test
  void givenPagination_whenSearchCars_thenReturnsPagedResults() {
    // given
    CarSearchCriteria criteria = CarSearchCriteria.builder().carBrand("Mercedes").build();

    Pageable firstPageable = PageRequest.of(0, 1);
    List<Car> firstPageCars = Collections.singletonList(car);
    Page<Car> firstPage = new PageImpl<>(firstPageCars, firstPageable, 2);

    when(carRepository.findAll(any(Specification.class), eq(firstPageable))).thenReturn(firstPage);

    // when
    Page<Car> result = carService.searchCars(criteria, firstPageable);

    // then
    assertNotNull(result);
    assertEquals(1, result.getContent().size());
    assertEquals(2, result.getTotalElements());
    assertEquals(0, result.getNumber());
    assertEquals(2, result.getTotalPages());
    assertTrue(result.hasNext());
    verify(carRepository, times(1)).findAll(any(Specification.class), eq(firstPageable));
  }
}
