package sarl.eazycar.items.domain.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.repository.ArticleRepository;

class ArticleServiceTest {

  @Mock private ArticleRepository articleRepository;

  @InjectMocks private ArticleService articleService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void getArticleById_WithValidId_ShouldReturnArticle() {
    // Arrange
    String articleId = "123456";
    Article expectedArticle = new Article();
    expectedArticle.setArticleId(articleId);
    expectedArticle.setName("Test Article");

    when(articleRepository.findById(articleId)).thenReturn(Optional.of(expectedArticle));

    // Act
    Article result = articleService.getArticleById(articleId);

    // Assert
    assertNotNull(result);
    assertEquals(articleId, result.getArticleId());
    assertEquals("Test Article", result.getName());
    verify(articleRepository, times(1)).findById(articleId);
  }

  @Test
  void getArticleById_WithNonExistentId_ShouldThrowException() {
    // Arrange
    String nonExistentId = "nonexistent";
    when(articleRepository.findById(nonExistentId)).thenReturn(Optional.empty());

    // Act & Assert
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> articleService.getArticleById(nonExistentId));

    assertEquals("Article with id " + nonExistentId + " not found", exception.getMessage());
    verify(articleRepository, times(1)).findById(nonExistentId);
  }

  @Test
  void getArticleById_WithNullId_ShouldThrowException() {
    // Act & Assert
    FunctionalErrorException exception =
        assertThrows(FunctionalErrorException.class, () -> articleService.getArticleById(null));

    assertEquals("Article ID cannot be null or empty", exception.getMessage());
  }

  @Test
  void getArticleById_WithEmptyId_ShouldThrowException() {
    // Arrange
    String emptyId = "";

    // Act & Assert
    FunctionalErrorException exception =
        assertThrows(FunctionalErrorException.class, () -> articleService.getArticleById(emptyId));

    assertEquals("Article ID cannot be null or empty", exception.getMessage());
    verify(articleRepository, times(0)).findById(emptyId);
  }

  /**
   * Test for getAllArticles method when articles exist Should return all articles from the
   * repository
   */
  @Test
  void getAllArticles_ShouldReturnAllArticles() {
    // Arrange
    List<Article> expectedArticles = new ArrayList<>();
    expectedArticles.add(createArticle("1", "Article 1", true, true));
    expectedArticles.add(createArticle("2", "Article 2", false, true));
    expectedArticles.add(createArticle("3", "Article 3", true, false));

    when(articleRepository.findAll()).thenReturn(expectedArticles);

    // Act
    List<Article> actualArticles = articleService.getAllArticles();

    // Assert
    assertEquals(expectedArticles.size(), actualArticles.size());
    assertEquals(expectedArticles, actualArticles);
    verify(articleRepository, times(1)).findAll();
  }

  /** Test for getAllArticles method when no articles exist Should return an empty list */
  @Test
  void getAllArticles_WhenNoArticles_ShouldReturnEmptyList() {
    // Arrange
    List<Article> emptyList = new ArrayList<>();
    when(articleRepository.findAll()).thenReturn(emptyList);

    // Act
    List<Article> result = articleService.getAllArticles();

    // Assert
    assertNotNull(result);
    assertTrue(result.isEmpty());
    verify(articleRepository, times(1)).findAll();
  }

  /** Helper method to create an Article with specified properties */
  private Article createArticle(String id, String name, boolean available, boolean visible) {
    Article article = new Article();
    article.setArticleId(id);
    article.setName(name);
    article.setAvailable(available);
    article.setVisible(visible);
    return article;
  }
}
