package sarl.eazycar.items.domain.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Catalog;
import sarl.eazycar.items.domain.repository.CatalogRepository;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
class CatalogServiceTest {

  @Mock private CatalogRepository catalogRepository;

  @InjectMocks private CatalogService catalogService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void givenCatalog_whenCreateCatalog_thenReturnCatalog() {
    // GIVEN
    var catalog =
        Catalog.builder().code("code").description("description").service("service").build();
    // WHEN
    when(catalogRepository.save(any(Catalog.class))).thenReturn(catalog);
    Catalog result = catalogService.createCatalog(catalog);
    // THEN
    verify(catalogRepository, times(1)).save(any(Catalog.class));
    assertEquals(catalog, result);
  }

  @Test
  void givenCatalogWithExistingId_whenCreateCatalog_thenThrowException() {
    // GIVEN
    var catalog =
        Catalog.builder()
            .catalogId("existingId")
            .code("code")
            .description("description")
            .service("service")
            .build();
    // WHEN & THEN
    assertThrows(FunctionalErrorException.class, () -> catalogService.createCatalog(catalog));
    verify(catalogRepository, never()).save(any(Catalog.class));
  }

  @Test
  void givenCatalogWithExistingCode_whenCreateCatalog_thenThrowException() {
    // GIVEN
    var catalog =
        Catalog.builder()
            .code("existingCode")
            .description("description")
            .service("service")
            .build();

    var existingCatalog =
        Catalog.builder()
            .catalogId("existingId")
            .code("existingCode")
            .description("existing description")
            .service("existing service")
            .build();

    // WHEN
    when(catalogRepository.findCatalogByCodeIgnoreCase("existingCode"))
        .thenReturn(Optional.of(existingCatalog));

    // THEN
    assertThrows(FunctionalErrorException.class, () -> catalogService.createCatalog(catalog));
    verify(catalogRepository, never()).save(any(Catalog.class));
  }

  @Test
  void givenValidId_whenGetCatalogById_thenReturnCatalog() {
    // GIVEN
    String catalogId = "validId";
    var catalog =
        Catalog.builder()
            .catalogId(catalogId)
            .code("code")
            .description("description")
            .service("service")
            .build();

    // WHEN
    when(catalogRepository.findById(catalogId)).thenReturn(Optional.of(catalog));
    Catalog result = catalogService.getCatalogById(catalogId);

    // THEN
    verify(catalogRepository, times(1)).findById(catalogId);
    assertEquals(catalog, result);
  }

  @Test
  void givenInvalidId_whenGetCatalogById_thenThrowException() {
    // GIVEN
    String catalogId = "invalidId";

    // WHEN
    when(catalogRepository.findById(catalogId)).thenReturn(Optional.empty());

    // THEN
    assertThrows(FunctionalErrorException.class, () -> catalogService.getCatalogById(catalogId));
    verify(catalogRepository, times(1)).findById(catalogId);
  }

  @Test
  void givenValidCode_whenGetCatalogByCode_thenReturnCatalog() {
    // GIVEN
    String catalogCode = "validCode";
    var catalog =
        Catalog.builder()
            .catalogId("id")
            .code(catalogCode)
            .description("description")
            .service("service")
            .build();

    // WHEN
    when(catalogRepository.findCatalogByCodeIgnoreCase(catalogCode))
        .thenReturn(Optional.of(catalog));
    Catalog result = catalogService.getCatalogByCode(catalogCode);

    // THEN
    verify(catalogRepository, times(1)).findCatalogByCodeIgnoreCase(catalogCode);
    assertEquals(catalog, result);
  }

  @Test
  void givenValidData_whenUpdateCatalog_thenReturnUpdatedCatalog() {
    // GIVEN
    String catalogId = "validId";
    var existingCatalog =
        Catalog.builder()
            .catalogId(catalogId)
            .code("code")
            .description("old description")
            .service("old service")
            .build();

    var updatedCatalog =
        Catalog.builder()
            .code("code")
            .description("new description")
            .service("new service")
            .build();

    var expectedCatalog =
        Catalog.builder()
            .catalogId(catalogId)
            .code("code")
            .description("new description")
            .service("new service")
            .build();

    // WHEN
    when(catalogRepository.findById(catalogId)).thenReturn(Optional.of(existingCatalog));
    when(catalogRepository.save(any(Catalog.class))).thenReturn(expectedCatalog);

    Catalog result = catalogService.updateCatalog(catalogId, updatedCatalog);

    // THEN
    verify(catalogRepository, times(1)).findById(catalogId);
    verify(catalogRepository, times(1)).save(any(Catalog.class));
    assertEquals(expectedCatalog, result);
  }

  @Test
  void givenBlankId_whenUpdateCatalog_thenThrowException() {
    // GIVEN
    String catalogId = "";
    var catalog =
        Catalog.builder().code("code").description("description").service("service").build();

    // WHEN & THEN
    assertThrows(
        FunctionalErrorException.class, () -> catalogService.updateCatalog(catalogId, catalog));
    verify(catalogRepository, never()).findById(anyString());
    verify(catalogRepository, never()).save(any(Catalog.class));
  }

  @Test
  void whenGetAllCatalogs_thenReturnAllCatalogs() {
    // GIVEN
    var catalog1 =
        Catalog.builder()
            .catalogId("id1")
            .code("code1")
            .description("description1")
            .service("service1")
            .build();

    var catalog2 =
        Catalog.builder()
            .catalogId("id2")
            .code("code2")
            .description("description2")
            .service("service2")
            .build();

    List<Catalog> expectedCatalogs = Arrays.asList(catalog1, catalog2);

    // WHEN
    when(catalogRepository.findAll()).thenReturn(expectedCatalogs);
    List<Catalog> result = catalogService.getAllCatalogs();

    // THEN
    verify(catalogRepository, times(1)).findAll();
    assertEquals(expectedCatalogs, result);
    assertEquals(2, result.size());
  }
}
