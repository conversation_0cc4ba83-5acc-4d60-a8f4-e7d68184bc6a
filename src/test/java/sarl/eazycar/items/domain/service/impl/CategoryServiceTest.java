package sarl.eazycar.items.domain.service.impl;

import static java.math.BigDecimal.valueOf;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Category;
import sarl.eazycar.items.domain.repository.CategoryRepository;

/**
 * <AUTHOR> FOGUE KAMGA <<EMAIL>>
 */
class CategoryServiceTest {

  @Mock private CategoryRepository categoryRepository;

  @InjectMocks private CategoryService categoryService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  // createCategory tests

  @Test
  void givenValidCategory_whenCreateCategory_thenReturnsSavedCategory() {
    // given
    Category category =
        Category.builder()
            .label("SUV")
            .type("SUV")
            .description("Sport Utility Vehicle")
            .minPrice(valueOf(20000))
            .maxPrice(valueOf(50000))
            .build();

    Category savedCategory =
        Category.builder()
            .categoryId("123456")
            .label("SUV")
            .type("SUV")
            .description("Sport Utility Vehicle")
            .minPrice(valueOf(20000))
            .maxPrice(valueOf(50000))
            .build();

    when(categoryRepository.findCategoryByTypeIgnoreCase(anyString())).thenReturn(Optional.empty());
    when(categoryRepository.save(any(Category.class))).thenReturn(savedCategory);

    // when
    Category result = categoryService.createCategory(category);

    // then
    assertNotNull(result);
    assertEquals("123456", result.getCategoryId());
    assertEquals("SUV", result.getType());
    verify(categoryRepository, times(1)).findCategoryByTypeIgnoreCase("SUV");
    verify(categoryRepository, times(1)).save(category);
  }

  @Test
  void givenCategoryWithId_whenCreateCategory_thenThrowsException() {
    // given
    Category category = Category.builder().categoryId("123456").label("SUV").type("SUV").build();

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> categoryService.createCategory(category));
    assertEquals("A new category cannot already have an ID.", exception.getMessage());
    verify(categoryRepository, never()).save(any(Category.class));
  }

  @Test
  void givenCategoryWithInvalidPriceRange_whenCreateCategory_thenThrowsException() {
    // given
    Category category =
        Category.builder()
            .label("SUV")
            .type("SUV")
            .minPrice(valueOf(50000))
            .maxPrice(valueOf(20000))
            .build();

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> categoryService.createCategory(category));
    assertEquals("The min price must be less than the max price.", exception.getMessage());
    verify(categoryRepository, never()).save(any(Category.class));
  }

  @Test
  void givenCategoryWithExistingType_whenCreateCategory_thenThrowsException() {
    // given
    Category category =
        Category.builder()
            .label("SUV")
            .type("SUV")
            .minPrice(valueOf(20000))
            .maxPrice(valueOf(50000))
            .build();

    Category existingCategory =
        Category.builder().categoryId("123456").label("SUV").type("SUV").build();

    when(categoryRepository.findCategoryByTypeIgnoreCase("SUV"))
        .thenReturn(Optional.of(existingCategory));

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> categoryService.createCategory(category));
    assertEquals("The category type SUV already exists.", exception.getMessage());
    verify(categoryRepository, times(1)).findCategoryByTypeIgnoreCase("SUV");
    verify(categoryRepository, never()).save(any(Category.class));
  }

  // getCategoryByType tests

  @Test
  void givenExistingType_whenGetCategoryByType_thenReturnsCategory() {
    // given
    String type = "SUV";
    Category category = Category.builder().categoryId("123456").label("SUV").type("SUV").build();

    when(categoryRepository.findCategoryByTypeIgnoreCase(type)).thenReturn(Optional.of(category));

    // when
    Category result = categoryService.getCategoryByType(type);

    // then
    assertNotNull(result);
    assertEquals("123456", result.getCategoryId());
    assertEquals("SUV", result.getType());
    verify(categoryRepository, times(1)).findCategoryByTypeIgnoreCase(type);
  }

  @Test
  void givenNonExistingType_whenGetCategoryByType_thenThrowsException() {
    // given
    String type = "NonExistingType";
    when(categoryRepository.findCategoryByTypeIgnoreCase(type)).thenReturn(Optional.empty());

    // when & then
    FunctionalErrorException exception =
        assertThrows(FunctionalErrorException.class, () -> categoryService.getCategoryByType(type));
    assertEquals("Category with type NonExistingType not found", exception.getMessage());
    verify(categoryRepository, times(1)).findCategoryByTypeIgnoreCase(type);
  }

  // getCategoryById tests

  @Test
  void givenExistingId_whenGetCategoryById_thenReturnsCategory() {
    // given
    String categoryId = "123456";
    Category category = Category.builder().categoryId(categoryId).label("SUV").type("SUV").build();

    when(categoryRepository.findById(categoryId)).thenReturn(Optional.of(category));

    // when
    Category result = categoryService.getCategoryById(categoryId);

    // then
    assertNotNull(result);
    assertEquals(categoryId, result.getCategoryId());
    assertEquals("SUV", result.getType());
    verify(categoryRepository, times(1)).findById(categoryId);
  }

  @Test
  void givenNonExistingId_whenGetCategoryById_thenThrowsException() {
    // given
    String categoryId = "nonExistingId";
    when(categoryRepository.findById(categoryId)).thenReturn(Optional.empty());

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> categoryService.getCategoryById(categoryId));
    assertEquals("Category with id nonExistingId not found", exception.getMessage());
    verify(categoryRepository, times(1)).findById(categoryId);
  }

  // updateCategory tests

  @Test
  void givenValidCategoryUpdate_whenUpdateCategory_thenReturnsUpdatedCategory() {
    // given
    String categoryId = "123456";
    Category existingCategory =
        Category.builder()
            .categoryId(categoryId)
            .label("SUV")
            .type("SUV")
            .description("Sport Utility Vehicle")
            .minPrice(valueOf(20000))
            .maxPrice(valueOf(50000))
            .build();

    Category updatedCategory =
        Category.builder()
            .label("Luxury SUV")
            .type("SUV")
            .description("Luxury Sport Utility Vehicle")
            .minPrice(valueOf(30000))
            .maxPrice(valueOf(80000))
            .build();

    Category savedCategory =
        Category.builder()
            .categoryId(categoryId)
            .label("Luxury SUV")
            .type("SUV")
            .description("Luxury Sport Utility Vehicle")
            .minPrice(valueOf(30000))
            .maxPrice(valueOf(80000))
            .build();

    when(categoryRepository.findById(categoryId)).thenReturn(Optional.of(existingCategory));
    when(categoryRepository.save(any(Category.class))).thenReturn(savedCategory);

    // when
    Category result = categoryService.updateCategory(categoryId, updatedCategory);

    // then
    assertNotNull(result);
    assertEquals(categoryId, result.getCategoryId());
    assertEquals("Luxury SUV", result.getLabel());
    assertEquals("Luxury Sport Utility Vehicle", result.getDescription());
    assertEquals(valueOf(30000), result.getMinPrice());
    assertEquals(valueOf(80000), result.getMaxPrice());
    verify(categoryRepository, times(1)).findById(categoryId);
    verify(categoryRepository, times(1)).save(any(Category.class));
  }

  @Test
  void givenNullCategoryId_whenUpdateCategory_thenThrowsException() {
    // given
    String categoryId = null;
    Category category = Category.builder().build();

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class,
            () -> categoryService.updateCategory(categoryId, category));
    assertEquals("Invalid id", exception.getMessage());
    verify(categoryRepository, never()).findById(anyString());
    verify(categoryRepository, never()).save(any(Category.class));
  }

  @Test
  void givenBlankCategoryId_whenUpdateCategory_thenThrowsException() {
    // given
    String categoryId = "";
    Category category = Category.builder().build();

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class,
            () -> categoryService.updateCategory(categoryId, category));
    assertEquals("Invalid id", exception.getMessage());
    verify(categoryRepository, never()).findById(anyString());
    verify(categoryRepository, never()).save(any(Category.class));
  }

  @Test
  void givenInvalidPriceRange_whenUpdateCategory_thenThrowsException() {
    // given
    String categoryId = "123456";
    Category category =
        Category.builder()
            .label("SUV")
            .type("SUV")
            .minPrice(valueOf(50000))
            .maxPrice(valueOf(20000))
            .build();

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class,
            () -> categoryService.updateCategory(categoryId, category));
    assertEquals("The min price must be less than the max price.", exception.getMessage());
    verify(categoryRepository, never()).findById(anyString());
    verify(categoryRepository, never()).save(any(Category.class));
  }

  @Test
  void givenChangedTypeAlreadyExists_whenUpdateCategory_thenThrowsException() {
    // given
    String categoryId = "123456";
    Category existingCategory =
        Category.builder()
            .categoryId(categoryId)
            .label("SUV")
            .type("SUV")
            .minPrice(valueOf(20000))
            .maxPrice(valueOf(50000))
            .build();

    Category updatedCategory =
        Category.builder()
            .label("Sedan")
            .type("SEDAN") // Changed type
            .minPrice(valueOf(15000))
            .maxPrice(valueOf(40000))
            .build();

    Category existingTypeCategory =
        Category.builder().categoryId("789012").label("Sedan").type("SEDAN").build();

    when(categoryRepository.findById(categoryId)).thenReturn(Optional.of(existingCategory));
    when(categoryRepository.findCategoryByTypeIgnoreCase("SEDAN"))
        .thenReturn(Optional.of(existingTypeCategory));

    // when & then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class,
            () -> categoryService.updateCategory(categoryId, updatedCategory));
    assertEquals("The category type SEDAN already exists.", exception.getMessage());
    verify(categoryRepository, times(1)).findById(categoryId);
    verify(categoryRepository, times(1)).findCategoryByTypeIgnoreCase("SEDAN");
    verify(categoryRepository, never()).save(any(Category.class));
  }

  // getAllCategories tests

  @Test
  void whenGetAllCategories_thenReturnsAllCategories() {
    // given
    Category category1 = Category.builder().categoryId("123456").label("SUV").type("SUV").build();

    Category category2 =
        Category.builder().categoryId("789012").label("Sedan").type("SEDAN").build();

    List<Category> categories = Arrays.asList(category1, category2);
    when(categoryRepository.findAll()).thenReturn(categories);

    // when
    List<Category> result = categoryService.getAllCategories();

    // then
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("123456", result.get(0).getCategoryId());
    assertEquals("789012", result.get(1).getCategoryId());
    verify(categoryRepository, times(1)).findAll();
  }

  @Test
  void whenGetAllCategories_andNoCategories_thenReturnsEmptyList() {
    // given
    when(categoryRepository.findAll()).thenReturn(List.of());

    // when
    List<Category> result = categoryService.getAllCategories();

    // then
    assertNotNull(result);
    assertTrue(result.isEmpty());
    verify(categoryRepository, times(1)).findAll();
  }
}
