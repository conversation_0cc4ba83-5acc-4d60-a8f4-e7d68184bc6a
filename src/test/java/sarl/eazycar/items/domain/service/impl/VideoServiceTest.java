package sarl.eazycar.items.domain.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Video;
import sarl.eazycar.items.domain.repository.VideoRepository;
import sarl.eazycar.items.domain.service.IArticleService;
import sarl.eazycar.items.infrastructure.config.ApplicationProperties;

/** Unit tests for {@link VideoService} */
class VideoServiceTest {

  @Mock private VideoRepository videoRepository;
  @Mock private IArticleService articleService;
  @Mock private ApplicationProperties applicationProperties;
  @Mock private ApplicationProperties.MediaSize mediaSize;

  @InjectMocks private VideoService videoService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    when(applicationProperties.getMediaSize()).thenReturn(mediaSize);
    when(mediaSize.getVideoLength()).thenReturn(2); // Default max videos
  }

  @Test
  void givenValidVideosList_whenCreateVideo_thenReturnsSavedVideos() {
    // Given
    String articleId = "article123";
    Article retrievedArticle = Article.builder().articleId(articleId).name("Test Article").build();

    List<Video> videos =
        Arrays.asList(
            Video.builder()
                .fileId("file1")
                .name("video1.mp4")
                .path("/path/to/video1.mp4")
                .format("mp4")
                .size(10240)
                .build(),
            Video.builder()
                .fileId("file2")
                .name("video2.avi")
                .path("/path/to/video2.avi")
                .format("avi")
                .size(20480)
                .build());

    when(videoRepository.countByArticleArticleId(articleId)).thenReturn(0);
    when(articleService.getArticleById(articleId)).thenReturn(retrievedArticle);
    when(videoRepository.saveAll(anyList())).thenReturn(videos);

    // When
    List<Video> result = videoService.createVideo(videos, articleId);

    // Then
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("file1", result.get(0).getFileId());
    assertEquals("video1.mp4", result.get(0).getName());
    assertEquals("file2", result.get(1).getFileId());
    assertEquals("video2.avi", result.get(1).getName());
    verify(articleService, times(1)).getArticleById(articleId);
    verify(videoRepository, times(1)).saveAll(videos);
  }

  @Test
  void givenExceedingMaxVideoLimit_whenCreateVideo_thenThrowsFunctionalErrorException() {
    // Given
    String articleId = "article123";

    List<Video> videos =
        Collections.singletonList(
            Video.builder()
                .fileId("file1")
                .name("video1.mp4")
                .path("/path/to/video1.mp4")
                .format("mp4")
                .size(10240)
                .build());

    // Configure the mock to return a count that equals the max limit
    when(videoRepository.countByArticleArticleId(articleId)).thenReturn(2);
    when(mediaSize.getVideoLength()).thenReturn(2);

    // When & Then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> videoService.createVideo(videos, articleId));

    assertEquals("Videos must be less than 2", exception.getMessage());
    verify(videoRepository, never()).saveAll(anyList());
    verify(articleService, never()).getArticleById(anyString());
  }

  @Test
  void givenArticleNotFound_whenCreateVideo_thenPropagatesException() {
    // Given
    String articleId = "nonexistent123";

    List<Video> videos =
        Collections.singletonList(
            Video.builder()
                .fileId("file1")
                .name("video1.mp4")
                .path("/path/to/video1.mp4")
                .format("mp4")
                .size(10240)
                .build());

    FunctionalErrorException notFoundException =
        new FunctionalErrorException("Article not found with ID: nonexistent123");
    when(articleService.getArticleById(articleId)).thenThrow(notFoundException);

    // When & Then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> videoService.createVideo(videos, articleId));

    assertEquals("Article not found with ID: nonexistent123", exception.getMessage());
    verify(videoRepository, never()).saveAll(anyList());
    verify(articleService, times(1)).getArticleById(articleId);
  }
}
