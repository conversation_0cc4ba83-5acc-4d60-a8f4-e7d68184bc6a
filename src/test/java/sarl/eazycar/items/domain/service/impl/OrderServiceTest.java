package sarl.eazycar.items.domain.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.*;
import sarl.eazycar.items.domain.entity.enums.CartStatus;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.repository.CartRepository;
import sarl.eazycar.items.domain.repository.OrderRepository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Tests unitaires pour OrderService
 */
@ExtendWith(MockitoExtension.class)
class OrderServiceTest {

    @Mock
    private OrderRepository orderRepository;

    @Mock
    private CartRepository cartRepository;

    @InjectMocks
    private OrderService orderService;

    private Purchase testPurchase;
    private Rental testRental;
    private Cart testCart;
    private Article testArticle;

    @BeforeEach
    void setUp() {
        testArticle = Article.builder()
                .articleId("article-123")
                .name("Test Car")
                .description("Test Description")
                .available(true)
                .build();

        testPurchase = new Purchase();
        testPurchase.setOrderId("order-123");
        testPurchase.setAccountId("account-123");
        testPurchase.setStatus(OrderStatus.PENDING);
        testPurchase.setQuantity(1);
        testPurchase.setUnitPrice(BigDecimal.valueOf(100.00));
        testPurchase.setCurrency("EUR");
        testPurchase.setArticle(testArticle);
        testPurchase.setOrderDate(LocalDateTime.now());
        testPurchase.setShippingAddress("123 Test Street");

        testRental = new Rental();
        testRental.setOrderId("rental-123");
        testRental.setAccountId("account-123");
        testRental.setStatus(OrderStatus.PENDING);
        testRental.setQuantity(1);
        testRental.setUnitPrice(BigDecimal.valueOf(50.00));
        testRental.setCurrency("EUR");
        testRental.setArticle(testArticle);
        testRental.setOrderDate(LocalDateTime.now());
        testRental.setStartDate(LocalDateTime.now().plusDays(1));
        testRental.setEndDate(LocalDateTime.now().plusDays(7));
        testRental.setPickupLocation("Pickup Location");
        testRental.setDropoffLocation("Dropoff Location");

        testCart = new Cart();
        testCart.setCartId("cart-123");
        testCart.setAccountId("account-123");
        testCart.setStatus(CartStatus.ACTIVE);
        testCart.setTotalAmount(BigDecimal.valueOf(100.00));
        testCart.getOrders().add(testPurchase);
        testPurchase.setCart(testCart);
    }

    @Test
    @DisplayName("Should create order from cart successfully")
    void shouldCreateOrderFromCartSuccessfully() {
        // Given
        when(cartRepository.findById("cart-123")).thenReturn(Optional.of(testCart));
        when(orderRepository.save(any(Order.class))).thenReturn(testPurchase);
        when(cartRepository.save(any(Cart.class))).thenReturn(testCart);

        // When
        Order result = orderService.createOrderFromCart("cart-123", "account-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo(OrderStatus.PENDING);
        assertThat(result.getConfirmationDate()).isNotNull();
        assertThat(testCart.getStatus()).isEqualTo(CartStatus.CHECKED_OUT);

        verify(cartRepository).findById("cart-123");
        verify(orderRepository).save(testPurchase);
        verify(cartRepository).save(testCart);
    }

    @Test
    @DisplayName("Should throw exception when cart ID is null")
    void shouldThrowExceptionWhenCartIdIsNull() {
        // When & Then
        assertThatThrownBy(() -> orderService.createOrderFromCart(null, "account-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Cart ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should throw exception when account ID is null")
    void shouldThrowExceptionWhenAccountIdIsNull() {
        // When & Then
        assertThatThrownBy(() -> orderService.createOrderFromCart("cart-123", null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Account ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should throw exception when cart not found")
    void shouldThrowExceptionWhenCartNotFound() {
        // Given
        when(cartRepository.findById("cart-123")).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> orderService.createOrderFromCart("cart-123", "account-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Cart not found: cart-123");
    }

    @Test
    @DisplayName("Should throw exception when cart does not belong to account")
    void shouldThrowExceptionWhenCartDoesNotBelongToAccount() {
        // Given
        testCart.setAccountId("other-account");
        when(cartRepository.findById("cart-123")).thenReturn(Optional.of(testCart));

        // When & Then
        assertThatThrownBy(() -> orderService.createOrderFromCart("cart-123", "account-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Cart does not belong to the specified account");
    }

    @Test
    @DisplayName("Should throw exception when cart is not active")
    void shouldThrowExceptionWhenCartIsNotActive() {
        // Given
        testCart.setStatus(CartStatus.CHECKED_OUT);
        when(cartRepository.findById("cart-123")).thenReturn(Optional.of(testCart));

        // When & Then
        assertThatThrownBy(() -> orderService.createOrderFromCart("cart-123", "account-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Cart is not active");
    }

    @Test
    @DisplayName("Should throw exception when cart is empty")
    void shouldThrowExceptionWhenCartIsEmpty() {
        // Given
        testCart.getOrders().clear();
        when(cartRepository.findById("cart-123")).thenReturn(Optional.of(testCart));

        // When & Then
        assertThatThrownBy(() -> orderService.createOrderFromCart("cart-123", "account-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Cart is empty");
    }

    @Test
    @DisplayName("Should get orders by account ID successfully")
    void shouldGetOrdersByAccountIdSuccessfully() {
        // Given
        List<Order> orders = List.of(testPurchase, testRental);
        when(orderRepository.findByAccountId("account-123")).thenReturn(orders);

        // When
        List<Order> result = orderService.getOrdersByAccountId("account-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result).containsExactly(testPurchase, testRental);
        verify(orderRepository).findByAccountId("account-123");
    }

    @Test
    @DisplayName("Should return empty list when no orders found for account")
    void shouldReturnEmptyListWhenNoOrdersFoundForAccount() {
        // Given
        when(orderRepository.findByAccountId("account-123")).thenReturn(List.of());

        // When
        List<Order> result = orderService.getOrdersByAccountId("account-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
        verify(orderRepository).findByAccountId("account-123");
    }

    @Test
    @DisplayName("Should throw exception when account ID is null for getOrdersByAccountId")
    void shouldThrowExceptionWhenAccountIdIsNullForGetOrdersByAccountId() {
        // When & Then
        assertThatThrownBy(() -> orderService.getOrdersByAccountId(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Account ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should get order by ID successfully")
    void shouldGetOrderByIdSuccessfully() {
        // Given
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));

        // When
        Order result = orderService.getOrderById("order-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getOrderId()).isEqualTo("order-123");
        verify(orderRepository).findById("order-123");
    }

    @Test
    @DisplayName("Should throw exception when order not found")
    void shouldThrowExceptionWhenOrderNotFound() {
        // Given
        when(orderRepository.findById("order-123")).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> orderService.getOrderById("order-123"))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order not found: order-123");
    }

    @Test
    @DisplayName("Should throw exception when order ID is null")
    void shouldThrowExceptionWhenOrderIdIsNull() {
        // When & Then
        assertThatThrownBy(() -> orderService.getOrderById(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Order ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should update order status successfully")
    void shouldUpdateOrderStatusSuccessfully() {
        // Given
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));
        when(orderRepository.save(any(Order.class))).thenReturn(testPurchase);

        // When
        Order result = orderService.updateOrderStatus("order-123", OrderStatus.CONFIRMED);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo(OrderStatus.CONFIRMED);
        assertThat(result.getConfirmationDate()).isNotNull();
        verify(orderRepository).findById("order-123");
        verify(orderRepository).save(testPurchase);
    }

    @Test
    @DisplayName("Should throw exception when new status is null")
    void shouldThrowExceptionWhenNewStatusIsNull() {
        // When & Then
        assertThatThrownBy(() -> orderService.updateOrderStatus("order-123", null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("New status cannot be null");
    }

    @Test
    @DisplayName("Should get orders by status successfully")
    void shouldGetOrdersByStatusSuccessfully() {
        // Given
        List<Order> orders = List.of(testPurchase);
        when(orderRepository.findByStatus(OrderStatus.PENDING)).thenReturn(orders);

        // When
        List<Order> result = orderService.getOrdersByStatus(OrderStatus.PENDING);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getStatus()).isEqualTo(OrderStatus.PENDING);
        verify(orderRepository).findByStatus(OrderStatus.PENDING);
    }

    @Test
    @DisplayName("Should throw exception when status is null for getOrdersByStatus")
    void shouldThrowExceptionWhenStatusIsNullForGetOrdersByStatus() {
        // When & Then
        assertThatThrownBy(() -> orderService.getOrdersByStatus(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Status cannot be null");
    }

    @Test
    @DisplayName("Should get orders by cart ID successfully")
    void shouldGetOrdersByCartIdSuccessfully() {
        // Given
        List<Order> orders = List.of(testPurchase);
        when(orderRepository.findByCartCartId("cart-123")).thenReturn(orders);

        // When
        List<Order> result = orderService.getOrdersByCartId("cart-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getCart().getCartId()).isEqualTo("cart-123");
        verify(orderRepository).findByCartCartId("cart-123");
    }

    @Test
    @DisplayName("Should throw exception when cart ID is null for getOrdersByCartId")
    void shouldThrowExceptionWhenCartIdIsNullForGetOrdersByCartId() {
        // When & Then
        assertThatThrownBy(() -> orderService.getOrdersByCartId(null))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Cart ID cannot be null or empty");
    }

    @Test
    @DisplayName("Should validate status transition from PENDING to CONFIRMED")
    void shouldValidateStatusTransitionFromPendingToConfirmed() {
        // Given
        testPurchase.setStatus(OrderStatus.PENDING);
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));
        when(orderRepository.save(any(Order.class))).thenReturn(testPurchase);

        // When
        Order result = orderService.updateOrderStatus("order-123", OrderStatus.CONFIRMED);

        // Then
        assertThat(result.getStatus()).isEqualTo(OrderStatus.CONFIRMED);
    }

    @Test
    @DisplayName("Should validate status transition from PENDING to CANCELLED")
    void shouldValidateStatusTransitionFromPendingToCancelled() {
        // Given
        testPurchase.setStatus(OrderStatus.PENDING);
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));
        when(orderRepository.save(any(Order.class))).thenReturn(testPurchase);

        // When
        Order result = orderService.updateOrderStatus("order-123", OrderStatus.CANCELLED);

        // Then
        assertThat(result.getStatus()).isEqualTo(OrderStatus.CANCELLED);
    }

    @Test
    @DisplayName("Should validate status transition from CONFIRMED to CANCELLED")
    void shouldValidateStatusTransitionFromConfirmedToCancelled() {
        // Given
        testPurchase.setStatus(OrderStatus.CONFIRMED);
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));
        when(orderRepository.save(any(Order.class))).thenReturn(testPurchase);

        // When
        Order result = orderService.updateOrderStatus("order-123", OrderStatus.CANCELLED);

        // Then
        assertThat(result.getStatus()).isEqualTo(OrderStatus.CANCELLED);
    }

    @Test
    @DisplayName("Should handle multiple orders in cart")
    void shouldHandleMultipleOrdersInCart() {
        // Given
        testCart.getOrders().add(testRental);
        testRental.setCart(testCart);

        when(cartRepository.findById("cart-123")).thenReturn(Optional.of(testCart));
        when(orderRepository.save(any(Order.class))).thenReturn(testPurchase);
        when(cartRepository.save(any(Cart.class))).thenReturn(testCart);

        // When
        Order result = orderService.createOrderFromCart("cart-123", "account-123");

        // Then
        assertThat(result).isNotNull();
        assertThat(testCart.getOrders()).hasSize(2);

        // Vérifier que tous les ordres ont été mis à jour
        testCart.getOrders().forEach(order -> {
            assertThat(order.getStatus()).isEqualTo(OrderStatus.PENDING);
            assertThat(order.getConfirmationDate()).isNotNull();
        });
    }

    @Test
    @DisplayName("Should throw exception for invalid status transition from IN_CART to CONFIRMED")
    void shouldThrowExceptionForInvalidStatusTransitionFromInCartToConfirmed() {
        // Given
        testPurchase.setStatus(OrderStatus.IN_CART);
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));

        // When & Then
        assertThatThrownBy(() -> orderService.updateOrderStatus("order-123", OrderStatus.CONFIRMED))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Invalid status transition from IN_CART to CONFIRMED");
    }

    @Test
    @DisplayName("Should throw exception for invalid status transition from PENDING to IN_CART")
    void shouldThrowExceptionForInvalidStatusTransitionFromPendingToInCart() {
        // Given
        testPurchase.setStatus(OrderStatus.PENDING);
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));

        // When & Then
        assertThatThrownBy(() -> orderService.updateOrderStatus("order-123", OrderStatus.IN_CART))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Invalid status transition from PENDING to IN_CART");
    }

    @Test
    @DisplayName("Should throw exception for invalid status transition from CONFIRMED to PENDING")
    void shouldThrowExceptionForInvalidStatusTransitionFromConfirmedToPending() {
        // Given
        testPurchase.setStatus(OrderStatus.CONFIRMED);
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));

        // When & Then
        assertThatThrownBy(() -> orderService.updateOrderStatus("order-123", OrderStatus.PENDING))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Invalid status transition from CONFIRMED to PENDING");
    }

    @Test
    @DisplayName("Should throw exception when trying to change status of cancelled order")
    void shouldThrowExceptionWhenTryingToChangeStatusOfCancelledOrder() {
        // Given
        testPurchase.setStatus(OrderStatus.CANCELLED);
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));

        // When & Then
        assertThatThrownBy(() -> orderService.updateOrderStatus("order-123", OrderStatus.PENDING))
                .isInstanceOf(FunctionalErrorException.class)
                .hasMessage("Cannot change status of a cancelled order");
    }

    @Test
    @DisplayName("Should allow transition from IN_CART to PENDING")
    void shouldAllowTransitionFromInCartToPending() {
        // Given
        testPurchase.setStatus(OrderStatus.IN_CART);
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));
        when(orderRepository.save(any(Order.class))).thenReturn(testPurchase);

        // When
        Order result = orderService.updateOrderStatus("order-123", OrderStatus.PENDING);

        // Then
        assertThat(result.getStatus()).isEqualTo(OrderStatus.PENDING);
    }

    @Test
    @DisplayName("Should allow transition from IN_CART to CANCELLED")
    void shouldAllowTransitionFromInCartToCancelled() {
        // Given
        testPurchase.setStatus(OrderStatus.IN_CART);
        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));
        when(orderRepository.save(any(Order.class))).thenReturn(testPurchase);

        // When
        Order result = orderService.updateOrderStatus("order-123", OrderStatus.CANCELLED);

        // Then
        assertThat(result.getStatus()).isEqualTo(OrderStatus.CANCELLED);
    }

    @Test
    @DisplayName("Should not update confirmation date when already set")
    void shouldNotUpdateConfirmationDateWhenAlreadySet() {
        // Given
        LocalDateTime existingConfirmationDate = LocalDateTime.now().minusDays(1);
        testPurchase.setStatus(OrderStatus.PENDING);
        testPurchase.setConfirmationDate(existingConfirmationDate);

        when(orderRepository.findById("order-123")).thenReturn(Optional.of(testPurchase));
        when(orderRepository.save(any(Order.class))).thenReturn(testPurchase);

        // When
        Order result = orderService.updateOrderStatus("order-123", OrderStatus.CONFIRMED);

        // Then
        assertThat(result.getStatus()).isEqualTo(OrderStatus.CONFIRMED);
        assertThat(result.getConfirmationDate()).isEqualTo(existingConfirmationDate);
    }
}
