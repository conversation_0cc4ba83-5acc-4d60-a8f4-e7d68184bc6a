package sarl.eazycar.items.domain.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Image;
import sarl.eazycar.items.domain.repository.ImageRepository;
import sarl.eazycar.items.infrastructure.config.ApplicationProperties;

/** Unit tests for {@link ImageService} */
class ImageServiceTest {

  @Mock private ImageRepository imageRepository;
  @Mock private ArticleService articleService;
  @Mock private ApplicationProperties applicationProperties;
  @Mock private ApplicationProperties.MediaSize mediaSize;

  @InjectMocks private ImageService imageService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    when(applicationProperties.getMediaSize()).thenReturn(mediaSize);
    when(mediaSize.getImageLength()).thenReturn(5); // Default max images
  }

  @Test
  void givenValidImageList_whenCreateImage_thenReturnsSavedImages() {
    // Given
    String articleId = "article123";

    Article retrievedArticle = Article.builder().articleId(articleId).name("Test Article").build();

    List<Image> images =
        Arrays.asList(
            Image.builder()
                .fileId("file1")
                .name("image1.jpg")
                .path("/path/to/image1.jpg")
                .format("jpg")
                .size(1024)
                .build(),
            Image.builder()
                .fileId("file2")
                .name("image2.png")
                .path("/path/to/image2.png")
                .format("png")
                .size(2048)
                .build());

    when(imageRepository.countByArticleArticleId(anyString())).thenReturn(0);
    when(articleService.getArticleById(anyString())).thenReturn(retrievedArticle);
    when(imageRepository.saveAll(anyList())).thenReturn(images);

    // When
    List<Image> result = imageService.createImage(images, articleId);

    // Then
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals("file1", result.get(0).getFileId());
    assertEquals("image1.jpg", result.get(0).getName());
    assertEquals("file2", result.get(1).getFileId());
    assertEquals("image2.png", result.get(1).getName());
    verify(articleService, times(1)).getArticleById("article123");
    verify(imageRepository, times(1)).saveAll(images);
  }

  @Test
  void givenArticleNotFound_whenCreateImage_thenPropagatesException() {
    // Given
    String articleId = "nonexistent123";

    List<Image> images =
        Collections.singletonList(
            Image.builder()
                .fileId("file1")
                .name("image1.jpg")
                .path("/path/to/image1.jpg")
                .format("jpg")
                .size(1024)
                .build());

    FunctionalErrorException notFoundException =
        new FunctionalErrorException("Article not found with ID: nonexistent123");
    when(articleService.getArticleById(articleId)).thenThrow(notFoundException);

    // When & Then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> imageService.createImage(images, articleId));

    assertEquals("Article not found with ID: nonexistent123", exception.getMessage());
    verify(imageRepository, never()).saveAll(anyList());
    verify(articleService, times(1)).getArticleById(articleId);
  }

  @Test
  void givenExceedingMaxImageLimit_whenCreateImage_thenThrowsFunctionalErrorException() {
    // Given
    String articleId = "article123";

    List<Image> images =
        Collections.singletonList(
            Image.builder()
                .fileId("file1")
                .name("image1.jpg")
                .path("/path/to/image1.jpg")
                .format("jpg")
                .size(1024)
                .build());

    // Configure the mock to return a count that equals the max limit
    when(imageRepository.countByArticleArticleId(articleId)).thenReturn(5);
    when(mediaSize.getImageLength()).thenReturn(5);

    // When & Then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> imageService.createImage(images, articleId));

    assertEquals("Images must be less than 5", exception.getMessage());
    verify(imageRepository, never()).saveAll(anyList());
    verify(articleService, never()).getArticleById(anyString());
  }

  @Test
  void givenZeroImageLimit_whenCreateImage_thenThrowsFunctionalErrorException() {
    // Given
    String articleId = "article123";

    List<Image> images =
        Collections.singletonList(
            Image.builder()
                .fileId("file1")
                .name("image1.jpg")
                .path("/path/to/image1.jpg")
                .format("jpg")
                .size(1024)
                .build());

    // Configure the mock to return zero as the max limit
    when(mediaSize.getImageLength()).thenReturn(0);

    // When & Then
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> imageService.createImage(images, articleId));

    assertEquals("Images must be less than 0", exception.getMessage());
    verify(imageRepository, never()).saveAll(anyList());
    verify(articleService, never()).getArticleById(anyString());
  }
}
