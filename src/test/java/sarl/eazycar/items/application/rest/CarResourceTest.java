package sarl.eazycar.items.application.rest;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.header;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import sarl.eazycar.items.application.dto.criteria.CarSearchCriteria;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.application.rest.request.CarRequest;
import sarl.eazycar.items.application.utils.RequestUriApi;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Car;
import sarl.eazycar.items.domain.service.ICarService;
import sarl.eazycar.items.infrastructure.factory.CarFactory;

/** Unit tests for {@link CarResource} using WebMvcTest */
@WebMvcTest(CarResource.class)
class CarResourceTest {

  @Autowired private MockMvc mockMvc;

  @Autowired private ObjectMapper objectMapper;

  @MockBean private ICarService carService;

  @MockBean private CarFactory carFactory;

  @Test
  void createCar_shouldReturnCreatedCar() throws Exception {
    // Given
    CarRequest carRequest =
        CarRequest.builder()
            .categoryId("category123")
            .brand("Toyota")
            .model("Camry")
            .factoryYear(2023)
            .name("Toyota Camry 2023")
            .description("Comfortable sedan with great fuel economy")
            .cityLocation("New York")
            .districtLocation("Manhattan")
            .available(true)
            .build();

    Article article =
        Article.builder()
            .articleId("article123")
            .name("Toyota Camry 2023")
            .description("Comfortable sedan with great fuel economy")
            .cityLocation("New York")
            .districtLocation("Manhattan")
            .available(true)
            .build();

    Car car =
        Car.builder()
            .carId("car123")
            .brand("Toyota")
            .model("Camry")
            .factoryYear(2023)
            .article(article)
            .build();

    Car savedCar =
        Car.builder()
            .carId("car123")
            .brand("Toyota")
            .model("Camry")
            .factoryYear(2023)
            .article(article)
            .build();

    // When
    when(carFactory.buildCar(any(CarRequest.class))).thenReturn(car);
    when(carService.createCar(any(Car.class))).thenReturn(savedCar);

    // Then
    mockMvc
        .perform(
            post(RequestUriApi.CAR_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(carRequest)))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.carId", is("car123")))
        .andExpect(jsonPath("$.brand", is("Toyota")))
        .andExpect(jsonPath("$.model", is("Camry")))
        .andExpect(jsonPath("$.factoryYear", is(2023)))
        .andExpect(jsonPath("$.article.articleId", is("article123")))
        .andExpect(jsonPath("$.article.name", is("Toyota Camry 2023")));

    verify(carFactory, times(1)).buildCar(any(CarRequest.class));
    verify(carService, times(1)).createCar(any(Car.class));
  }

  @Test
  void updateCar_shouldReturnUpdatedCar() throws Exception {
    // Given
    String carId = "car123";
    CarRequest carRequest =
        CarRequest.builder()
            .categoryId("category123")
            .brand("Toyota")
            .model("Camry Hybrid")
            .factoryYear(2023)
            .name("Toyota Camry Hybrid 2023")
            .description("Updated description - Hybrid sedan with excellent fuel economy")
            .cityLocation("New York")
            .districtLocation("Manhattan")
            .available(true)
            .build();

    Article article =
        Article.builder()
            .articleId("article123")
            .name("Toyota Camry Hybrid 2023")
            .description("Updated description - Hybrid sedan with excellent fuel economy")
            .cityLocation("New York")
            .districtLocation("Manhattan")
            .available(true)
            .build();

    Car car =
        Car.builder()
            .carId(carId)
            .brand("Toyota")
            .model("Camry Hybrid")
            .factoryYear(2023)
            .article(article)
            .build();

    Car updatedCar =
        Car.builder()
            .carId(carId)
            .brand("Toyota")
            .model("Camry Hybrid")
            .factoryYear(2023)
            .article(article)
            .build();

    // When
    when(carFactory.buildCar(any(CarRequest.class))).thenReturn(car);
    when(carService.updateCar(eq(carId), any(Car.class))).thenReturn(updatedCar);

    // Then
    mockMvc
        .perform(
            put(RequestUriApi.CAR_ENDPOINT + "/" + carId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(carRequest)))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.carId", is(carId)))
        .andExpect(jsonPath("$.brand", is("Toyota")))
        .andExpect(jsonPath("$.model", is("Camry Hybrid")))
        .andExpect(jsonPath("$.factoryYear", is(2023)))
        .andExpect(jsonPath("$.article.articleId", is("article123")))
        .andExpect(jsonPath("$.article.name", is("Toyota Camry Hybrid 2023")));

    verify(carFactory, times(1)).buildCar(any(CarRequest.class));
    verify(carService, times(1)).updateCar(eq(carId), any(Car.class));
  }

  @Test
  void getCarById_shouldReturnCar() throws Exception {
    // Given
    String carId = "car123";
    Article article =
        Article.builder()
            .articleId("article123")
            .name("Toyota Camry 2023")
            .description("Comfortable sedan with great fuel economy")
            .cityLocation("New York")
            .districtLocation("Manhattan")
            .available(true)
            .build();

    Car foundCar =
        Car.builder()
            .carId(carId)
            .brand("Toyota")
            .model("Camry")
            .factoryYear(2023)
            .article(article)
            .build();

    // When
    when(carService.getCarById(carId)).thenReturn(foundCar);

    // Then
    mockMvc
        .perform(get(RequestUriApi.CAR_ENDPOINT + "/" + carId).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.carId", is(carId)))
        .andExpect(jsonPath("$.brand", is("Toyota")))
        .andExpect(jsonPath("$.model", is("Camry")))
        .andExpect(jsonPath("$.factoryYear", is(2023)))
        .andExpect(jsonPath("$.article.articleId", is("article123")))
        .andExpect(jsonPath("$.article.name", is("Toyota Camry 2023")));

    verify(carService, times(1)).getCarById(carId);
  }

  @Test
  void getAllCars_shouldReturnAllCars() throws Exception {
    // Given
    Article article1 =
        Article.builder()
            .articleId("article123")
            .name("Toyota Camry 2023")
            .description("Comfortable sedan with great fuel economy")
            .cityLocation("New York")
            .districtLocation("Manhattan")
            .available(true)
            .build();

    Article article2 =
        Article.builder()
            .articleId("article456")
            .name("Honda Accord 2023")
            .description("Reliable sedan with advanced safety features")
            .cityLocation("Los Angeles")
            .districtLocation("Downtown")
            .available(true)
            .build();

    Car car1 =
        Car.builder()
            .carId("car123")
            .brand("Toyota")
            .model("Camry")
            .factoryYear(2023)
            .article(article1)
            .build();

    Car car2 =
        Car.builder()
            .carId("car456")
            .brand("Honda")
            .model("Accord")
            .factoryYear(2023)
            .article(article2)
            .build();

    List<Car> cars = Arrays.asList(car1, car2);

    // When
    when(carService.getAllCars()).thenReturn(cars);

    // Then
    mockMvc
        .perform(get(RequestUriApi.CAR_ENDPOINT).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$", hasSize(2)))
        .andExpect(jsonPath("$[0].carId", is("car123")))
        .andExpect(jsonPath("$[0].brand", is("Toyota")))
        .andExpect(jsonPath("$[0].model", is("Camry")))
        .andExpect(jsonPath("$[0].factoryYear", is(2023)))
        .andExpect(jsonPath("$[0].article.articleId", is("article123")))
        .andExpect(jsonPath("$[0].article.name", is("Toyota Camry 2023")))
        .andExpect(jsonPath("$[1].carId", is("car456")))
        .andExpect(jsonPath("$[1].brand", is("Honda")))
        .andExpect(jsonPath("$[1].model", is("Accord")))
        .andExpect(jsonPath("$[1].factoryYear", is(2023)))
        .andExpect(jsonPath("$[1].article.articleId", is("article456")))
        .andExpect(jsonPath("$[1].article.name", is("Honda Accord 2023")));

    verify(carService, times(1)).getAllCars();
  }

  @Test
  void createCar_whenServiceThrowsException_shouldReturnError() throws Exception {
    // Given
    CarRequest carRequest =
        CarRequest.builder()
            .categoryId("invalid-category")
            .brand("Toyota")
            .model("Camry")
            .factoryYear(2023)
            .name("Toyota Camry 2023")
            .description("Comfortable sedan with great fuel economy")
            .cityLocation("New York")
            .districtLocation("Manhattan")
            .available(true)
            .build();

    Car car = Car.builder().brand("Toyota").model("Camry").factoryYear(2023).build();

    // When
    when(carFactory.buildCar(any(CarRequest.class))).thenReturn(car);
    when(carService.createCar(any(Car.class)))
        .thenThrow(new FunctionalErrorException("Category with id invalid-category not found"));

    // Then
    mockMvc
        .perform(
            post(RequestUriApi.CAR_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(carRequest)))
        .andExpect(status().isBadRequest());

    verify(carFactory, times(1)).buildCar(any(CarRequest.class));
    verify(carService, times(1)).createCar(any(Car.class));
  }

  @Test
  void updateCar_whenServiceThrowsNotFoundException_shouldReturnError() throws Exception {
    // Given
    String carId = "non-existent-id";
    CarRequest carRequest =
        CarRequest.builder()
            .categoryId("category123")
            .brand("Toyota")
            .model("Camry")
            .factoryYear(2023)
            .name("Toyota Camry 2023")
            .description("Comfortable sedan with great fuel economy")
            .cityLocation("New York")
            .districtLocation("Manhattan")
            .available(true)
            .build();

    Car car = Car.builder().brand("Toyota").model("Camry").factoryYear(2023).build();

    // When
    when(carFactory.buildCar(any(CarRequest.class))).thenReturn(car);
    when(carService.updateCar(eq(carId), any(Car.class)))
        .thenThrow(new FunctionalErrorException("Car with id " + carId + " not found"));

    // Then
    mockMvc
        .perform(
            put(RequestUriApi.CAR_ENDPOINT + "/" + carId)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(carRequest)))
        .andExpect(status().isBadRequest());

    verify(carFactory, times(1)).buildCar(any(CarRequest.class));
    verify(carService, times(1)).updateCar(eq(carId), any(Car.class));
  }

  @Test
  void getCarById_whenServiceThrowsNotFoundException_shouldReturnError() throws Exception {
    // Given
    String carId = "non-existent-id";

    // When
    when(carService.getCarById(carId))
        .thenThrow(new FunctionalErrorException("Car with id " + carId + " not found"));

    // Then
    mockMvc
        .perform(get(RequestUriApi.CAR_ENDPOINT + "/" + carId).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());

    verify(carService, times(1)).getCarById(carId);
  }

  @Test
  void getAllCars_whenServiceThrowsUnexpectedException_shouldReturnError() throws Exception {
    // Given
    RuntimeException exception = new RuntimeException("Database connection error");

    // When
    when(carService.getAllCars()).thenThrow(exception);

    // Then
    mockMvc
        .perform(get(RequestUriApi.CAR_ENDPOINT).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isInternalServerError());

    verify(carService, times(1)).getAllCars();
  }

  @Test
  void searchCars_withValidCriteria_shouldReturnMatchingCars() throws Exception {
    // Given
    Pageable pageable = PageRequest.of(0, 10);

    Article article1 =
        Article.builder()
            .articleId("article123")
            .name("Toyota Camry 2023")
            .description("Comfortable sedan with great fuel economy")
            .cityLocation("New York")
            .districtLocation("Manhattan")
            .available(true)
            .build();

    Car car1 =
        Car.builder()
            .carId("car123")
            .brand("Toyota")
            .model("Camry")
            .factoryYear(2023)
            .article(article1)
            .build();

    List<Car> cars = Collections.singletonList(car1);
    Page<Car> carPage = new PageImpl<>(cars, pageable, 1);

    // When
    when(carService.searchCars(any(CarSearchCriteria.class), any(Pageable.class)))
        .thenReturn(carPage);

    // Then
    mockMvc
        .perform(
            get(RequestUriApi.CAR_ENDPOINT_WITH_SEARCH_VARIABLE)
                .param("carBrand", "Toyota")
                .param("carModel", "Camry")
                .param("locationCity", "New York")
                .param("availableOnly", "true")
                .param("page", "0")
                .param("size", "10")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$", hasSize(1)))
        .andExpect(jsonPath("$[0].carId", is("car123")))
        .andExpect(jsonPath("$[0].brand", is("Toyota")))
        .andExpect(jsonPath("$[0].model", is("Camry")))
        .andExpect(jsonPath("$[0].factoryYear", is(2023)))
        .andExpect(header().exists("X-Total-Count"))
        .andExpect(header().exists("Link"));

    verify(carService, times(1)).searchCars(any(CarSearchCriteria.class), any(Pageable.class));
  }

  @Test
  void searchCars_withEmptyResult_shouldReturnEmptyList() throws Exception {
    // Given
    Pageable pageable = PageRequest.of(0, 10);
    Page<Car> emptyPage = new PageImpl<>(Collections.emptyList(), pageable, 0);

    // When
    when(carService.searchCars(any(CarSearchCriteria.class), any(Pageable.class)))
        .thenReturn(emptyPage);

    // Then
    mockMvc
        .perform(
            get(RequestUriApi.CAR_ENDPOINT_WITH_SEARCH_VARIABLE)
                .param("carBrand", "NonExistentBrand")
                .param("page", "0")
                .param("size", "10")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$", hasSize(0)))
        .andExpect(header().exists("X-Total-Count"))
        .andExpect(header().string("X-Total-Count", "0"));

    verify(carService, times(1)).searchCars(any(CarSearchCriteria.class), any(Pageable.class));
  }

  @Test
  void searchCars_withPaginationParameters_shouldReturnPagedResults() throws Exception {
    // Given
    Pageable pageable = PageRequest.of(1, 2); // Page 1 with 2 items per page

    Article article1 =
        Article.builder()
            .articleId("article123")
            .name("Toyota Camry 2023")
            .description("Comfortable sedan with great fuel economy")
            .cityLocation("New York")
            .districtLocation("Manhattan")
            .available(true)
            .build();

    Article article2 =
        Article.builder()
            .articleId("article456")
            .name("Honda Accord 2023")
            .description("Reliable sedan with advanced safety features")
            .cityLocation("Los Angeles")
            .districtLocation("Downtown")
            .available(true)
            .build();

    Car car1 =
        Car.builder()
            .carId("car123")
            .brand("Toyota")
            .model("Camry")
            .factoryYear(2023)
            .article(article1)
            .build();

    Car car2 =
        Car.builder()
            .carId("car456")
            .brand("Honda")
            .model("Accord")
            .factoryYear(2023)
            .article(article2)
            .build();

    List<Car> cars = Arrays.asList(car1, car2);
    Page<Car> carPage =
        new PageImpl<>(cars, pageable, 5); // Total 5 items, showing page 1 (items 3-4)

    // When
    when(carService.searchCars(any(CarSearchCriteria.class), any(Pageable.class)))
        .thenReturn(carPage);

    // Then
    mockMvc
        .perform(
            get(RequestUriApi.CAR_ENDPOINT_WITH_SEARCH_VARIABLE)
                .param("page", "1")
                .param("size", "2")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$", hasSize(2)))
        .andExpect(header().exists("X-Total-Count"))
        .andExpect(header().string("X-Total-Count", "5"))
        .andExpect(header().exists("Link"));

    verify(carService, times(1)).searchCars(any(CarSearchCriteria.class), any(Pageable.class));
  }

  @Test
  void searchCars_withNullCriteria_shouldUseDefaultValues() throws Exception {
    // Given
    Pageable pageable = PageRequest.of(0, 10);

    Article article1 =
        Article.builder()
            .articleId("article123")
            .name("Toyota Camry 2023")
            .description("Comfortable sedan with great fuel economy")
            .cityLocation("New York")
            .districtLocation("Manhattan")
            .available(true)
            .build();

    Car car1 =
        Car.builder()
            .carId("car123")
            .brand("Toyota")
            .model("Camry")
            .factoryYear(2023)
            .article(article1)
            .build();

    List<Car> cars = Collections.singletonList(car1);
    Page<Car> carPage = new PageImpl<>(cars, pageable, 1);

    // When - service should handle null criteria with default values
    when(carService.searchCars(any(CarSearchCriteria.class), any(Pageable.class)))
        .thenReturn(carPage);

    // Then
    mockMvc
        .perform(
            get(RequestUriApi.CAR_ENDPOINT_WITH_SEARCH_VARIABLE)
                .param("page", "0")
                .param("size", "10")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$", hasSize(1)));

    verify(carService, times(1)).searchCars(any(CarSearchCriteria.class), any(Pageable.class));
  }

  @Test
  void searchCars_withMultipleFilterCriteria_shouldReturnFilteredResults() throws Exception {
    // Given
    Pageable pageable = PageRequest.of(0, 10);

    Article article1 =
        Article.builder()
            .articleId("article123")
            .name("Toyota Camry 2023")
            .description("Comfortable sedan with great fuel economy")
            .cityLocation("New York")
            .districtLocation("Manhattan")
            .available(true)
            .build();

    Car car1 =
        Car.builder()
            .carId("car123")
            .brand("Toyota")
            .model("Camry")
            .factoryYear(2023)
            .article(article1)
            .build();

    List<Car> cars = Collections.singletonList(car1);
    Page<Car> carPage = new PageImpl<>(cars, pageable, 1);

    // When
    when(carService.searchCars(any(CarSearchCriteria.class), any(Pageable.class)))
        .thenReturn(carPage);

    // Then
    mockMvc
        .perform(
            get(RequestUriApi.CAR_ENDPOINT_WITH_SEARCH_VARIABLE)
                .param("carBrand", "Toyota")
                .param("minFactoryYear", "2020")
                .param("maxFactoryYear", "2023")
                .param("minPrice", "20000")
                .param("maxPrice", "30000")
                .param("locationCity", "New York")
                .param("locationDistrict", "Manhattan")
                .param("categoryType", "Sedan")
                .param("availableOnly", "true")
                .param("page", "0")
                .param("size", "10")
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$", hasSize(1)))
        .andExpect(jsonPath("$[0].carId", is("car123")))
        .andExpect(jsonPath("$[0].brand", is("Toyota")))
        .andExpect(jsonPath("$[0].model", is("Camry")))
        .andExpect(jsonPath("$[0].factoryYear", is(2023)));

    verify(carService, times(1)).searchCars(any(CarSearchCriteria.class), any(Pageable.class));
  }
}
