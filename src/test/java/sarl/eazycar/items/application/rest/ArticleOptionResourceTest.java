package sarl.eazycar.items.application.rest;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import sarl.eazycar.items.application.dto.ArticleOptionDto;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.application.utils.RequestUriApi;
import sarl.eazycar.items.domain.entity.ArticleOption;
import sarl.eazycar.items.domain.service.impl.ArticleOptionService;

/** Unit tests for {@link ArticleOptionResource} using WebMvcTest */
@WebMvcTest(ArticleOptionResource.class)
class ArticleOptionResourceTest {

  @Autowired private MockMvc mockMvc;

  @Autowired private ObjectMapper objectMapper;

  @MockBean private ArticleOptionService articleOptionService;

  @Test
  void createArticleOption_shouldReturnCreatedArticleOption() throws Exception {
    // Given
    ArticleOptionDto inputDto =
        ArticleOptionDto.builder()
            .optionName("Premium Sound System")
            .optionDescription("High-quality sound system with subwoofer")
            .build();

    ArticleOption savedEntity =
        ArticleOption.builder()
            .optionId("123456")
            .optionName("Premium Sound System")
            .optionDescription("High-quality sound system with subwoofer")
            .build();

    // When
    when(articleOptionService.createArticleOption(any(ArticleOption.class)))
        .thenReturn(savedEntity);

    // Then
    mockMvc
        .perform(
            post(RequestUriApi.ARTICLE_OPTION_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(inputDto)))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.optionId", is("123456")))
        .andExpect(jsonPath("$.optionName", is("Premium Sound System")))
        .andExpect(jsonPath("$.optionDescription", is("High-quality sound system with subwoofer")));

    verify(articleOptionService, times(1)).createArticleOption(any(ArticleOption.class));
  }

  @Test
  void updateArticleOption_shouldReturnUpdatedArticleOption() throws Exception {
    // Given
    String articleOptionId = "123456";
    ArticleOptionDto inputDto =
        ArticleOptionDto.builder()
            .optionName("Updated Sound System")
            .optionDescription("Updated high-quality sound system with subwoofer")
            .build();

    ArticleOption updatedEntity =
        ArticleOption.builder()
            .optionId(articleOptionId)
            .optionName("Updated Sound System")
            .optionDescription("Updated high-quality sound system with subwoofer")
            .build();

    // When
    when(articleOptionService.updateArticleOption(eq(articleOptionId), any(ArticleOption.class)))
        .thenReturn(updatedEntity);

    // Then
    mockMvc
        .perform(
            put(RequestUriApi.ARTICLE_OPTION_ENDPOINT + "/" + articleOptionId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(inputDto)))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.optionId", is(articleOptionId)))
        .andExpect(jsonPath("$.optionName", is("Updated Sound System")))
        .andExpect(
            jsonPath(
                "$.optionDescription", is("Updated high-quality sound system with subwoofer")));

    verify(articleOptionService, times(1))
        .updateArticleOption(eq(articleOptionId), any(ArticleOption.class));
  }

  @Test
  void getArticleOptionById_shouldReturnArticleOption() throws Exception {
    // Given
    String articleOptionId = "123456";
    ArticleOption foundEntity =
        ArticleOption.builder()
            .optionId(articleOptionId)
            .optionName("Premium Sound System")
            .optionDescription("High-quality sound system with subwoofer")
            .build();

    // When
    when(articleOptionService.getArticleOptionById(articleOptionId)).thenReturn(foundEntity);

    // Then
    mockMvc
        .perform(
            get(RequestUriApi.ARTICLE_OPTION_ENDPOINT + "/" + articleOptionId)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.optionId", is(articleOptionId)))
        .andExpect(jsonPath("$.optionName", is("Premium Sound System")))
        .andExpect(jsonPath("$.optionDescription", is("High-quality sound system with subwoofer")));

    verify(articleOptionService, times(1)).getArticleOptionById(articleOptionId);
  }

  @Test
  void getAllArticleOptions_shouldReturnAllArticleOptions() throws Exception {
    // Given
    ArticleOption option1 =
        ArticleOption.builder()
            .optionId("123456")
            .optionName("Premium Sound System")
            .optionDescription("High-quality sound system with subwoofer")
            .build();

    ArticleOption option2 =
        ArticleOption.builder()
            .optionId("789012")
            .optionName("Navigation System")
            .optionDescription("GPS navigation with real-time traffic updates")
            .build();

    List<ArticleOption> options = Arrays.asList(option1, option2);

    // When
    when(articleOptionService.getAllArticleOptions()).thenReturn(options);

    // Then
    mockMvc
        .perform(get(RequestUriApi.ARTICLE_OPTION_ENDPOINT).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$", hasSize(2)))
        .andExpect(jsonPath("$[0].optionId", is("123456")))
        .andExpect(jsonPath("$[0].optionName", is("Premium Sound System")))
        .andExpect(
            jsonPath("$[0].optionDescription", is("High-quality sound system with subwoofer")))
        .andExpect(jsonPath("$[1].optionId", is("789012")))
        .andExpect(jsonPath("$[1].optionName", is("Navigation System")))
        .andExpect(
            jsonPath(
                "$[1].optionDescription", is("GPS navigation with real-time traffic updates")));

    verify(articleOptionService, times(1)).getAllArticleOptions();
  }

  @Test
  void createArticleOption_whenServiceThrowsExistingNameException_shouldReturnError()
      throws Exception {
    // Given
    ArticleOptionDto inputDto =
        ArticleOptionDto.builder()
            .optionName("Existing Option")
            .optionDescription("This option name already exists")
            .build();

    // When
    when(articleOptionService.createArticleOption(any(ArticleOption.class)))
        .thenThrow(
            new FunctionalErrorException("The articleOption name Existing Option already exists."));

    // Then
    mockMvc
        .perform(
            post(RequestUriApi.ARTICLE_OPTION_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(inputDto)))
        .andExpect(status().isBadRequest());

    verify(articleOptionService, times(1)).createArticleOption(any(ArticleOption.class));
  }

  @Test
  void updateArticleOption_whenServiceThrowsExistingNameException_shouldReturnError()
      throws Exception {
    // Given
    String articleOptionId = "123456";
    ArticleOptionDto inputDto =
        ArticleOptionDto.builder()
            .optionName("Existing Option")
            .optionDescription("This option name already exists")
            .build();

    // When
    when(articleOptionService.updateArticleOption(eq(articleOptionId), any(ArticleOption.class)))
        .thenThrow(
            new FunctionalErrorException("The articleOption name Existing Option already exists."));

    // Then
    mockMvc
        .perform(
            put(RequestUriApi.ARTICLE_OPTION_ENDPOINT + "/" + articleOptionId)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(inputDto)))
        .andExpect(status().isBadRequest());

    verify(articleOptionService, times(1))
        .updateArticleOption(eq(articleOptionId), any(ArticleOption.class));
  }

  @Test
  void getArticleOptionById_whenServiceThrowsNotFoundException_shouldReturnError()
      throws Exception {
    // Given
    String articleOptionId = "non-existent-id";

    // When
    when(articleOptionService.getArticleOptionById(articleOptionId))
        .thenThrow(
            new FunctionalErrorException(
                "ArticleOption with id " + articleOptionId + " not found"));

    // Then
    mockMvc
        .perform(
            get(RequestUriApi.ARTICLE_OPTION_ENDPOINT + "/" + articleOptionId)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());

    verify(articleOptionService, times(1)).getArticleOptionById(articleOptionId);
  }

  @Test
  void getAllArticleOptions_whenServiceThrowsUnexpectedException_shouldReturnError()
      throws Exception {
    // Given
    RuntimeException exception = new RuntimeException("Database connection error");

    // When
    when(articleOptionService.getAllArticleOptions()).thenThrow(exception);

    // Then
    mockMvc
        .perform(get(RequestUriApi.ARTICLE_OPTION_ENDPOINT).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isInternalServerError());

    verify(articleOptionService, times(1)).getAllArticleOptions();
  }
}
