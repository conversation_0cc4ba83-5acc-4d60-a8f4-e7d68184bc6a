package sarl.eazycar.items.application.rest;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import sarl.eazycar.items.application.dto.VideoDto;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.application.rest.request.VideoRequest;
import sarl.eazycar.items.application.utils.RequestUriApi;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Video;
import sarl.eazycar.items.domain.service.IVideoService;
import sarl.eazycar.items.infrastructure.factory.VideoFactory;

/** Unit tests for {@link VideoResource} using WebMvcTest */
@WebMvcTest(VideoResource.class)
class VideoResourceTest {

  @Autowired private MockMvc mockMvc;

  @Autowired private ObjectMapper objectMapper;

  @MockBean private IVideoService videoService;

  @MockBean private VideoFactory videoFactory;

  @Test
  void createVideos_shouldReturnCreatedVideos() throws Exception {
    // Given
    String articleId = "article123";

    VideoDto videoDto1 =
        VideoDto.builder()
            .name("Car Front View")
            .format("mp4")
            .path("/videos/car-front.mp4")
            .size(1024)
            .build();

    VideoDto videoDto2 =
        VideoDto.builder()
            .name("Car Interior View")
            .format("mp4")
            .path("/videos/car-interior.mp4")
            .size(2048)
            .build();

    List<VideoDto> videoDtos = Arrays.asList(videoDto1, videoDto2);

    VideoRequest request = VideoRequest.builder().articleId(articleId).videos(videoDtos).build();

    Article article = Article.builder().articleId(articleId).build();

    Video video1 =
        Video.builder()
            .fileId("video123")
            .name("Car Front View")
            .format("mp4")
            .path("/videos/car-front.mp4")
            .size(1024)
            .article(article)
            .build();

    Video video2 =
        Video.builder()
            .fileId("video456")
            .name("Car Interior View")
            .format("mp4")
            .path("/videos/car-interior.mp4")
            .size(2048)
            .article(article)
            .build();

    List<Video> videos = Arrays.asList(video1, video2);

    // When
    when(videoFactory.buildVideo(any(VideoRequest.class))).thenReturn(videos);
    when(videoService.createVideo(any(), any())).thenReturn(videos);

    // Then
    mockMvc
        .perform(
            post(RequestUriApi.VIDEO_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$", hasSize(2)))
        .andExpect(jsonPath("$[0].fileId", is("video123")))
        .andExpect(jsonPath("$[0].name", is("Car Front View")))
        .andExpect(jsonPath("$[0].path", is("/videos/car-front.mp4")))
        .andExpect(jsonPath("$[0].format", is("mp4")))
        .andExpect(jsonPath("$[0].size", is(1024)))
        .andExpect(jsonPath("$[1].fileId", is("video456")))
        .andExpect(jsonPath("$[1].name", is("Car Interior View")))
        .andExpect(jsonPath("$[1].path", is("/videos/car-interior.mp4")))
        .andExpect(jsonPath("$[1].format", is("mp4")))
        .andExpect(jsonPath("$[1].size", is(2048)));

    verify(videoFactory, times(1)).buildVideo(any(VideoRequest.class));
    verify(videoService, times(1)).createVideo(any(), any());
  }

  @Test
  void createVideos_withInvalidArticleId_shouldReturnError() throws Exception {
    // Given
    String invalidArticleId = "invalid-article-id";

    VideoDto videoDto =
        VideoDto.builder()
            .name("Car Front View")
            .format("mp4")
            .path("/videos/car-front.mp4")
            .size(1024)
            .build();

    VideoRequest request =
        VideoRequest.builder()
            .articleId(invalidArticleId)
            .videos(Collections.singletonList(videoDto))
            .build();

    // When
    when(videoFactory.buildVideo(any(VideoRequest.class)))
        .thenThrow(
            new FunctionalErrorException("Article with id " + invalidArticleId + " not found"));

    // Then
    mockMvc
        .perform(
            post(RequestUriApi.VIDEO_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest());

    verify(videoFactory, times(1)).buildVideo(any(VideoRequest.class));
  }

  @Test
  void createVideos_whenServiceThrowsException_shouldReturnError() throws Exception {
    // Given
    String articleId = "article123";

    VideoDto videoDto =
        VideoDto.builder()
            .name("Car Front View")
            .format("mp4")
            .path("/videos/car-front.mp4")
            .size(1024)
            .build();

    VideoRequest request =
        VideoRequest.builder()
            .articleId(articleId)
            .videos(Collections.singletonList(videoDto))
            .build();

    List<Video> videos = new ArrayList<>();
    Video video =
        Video.builder()
            .name("Car Front View")
            .format("mp4")
            .path("/videos/car-front.mp4")
            .size(1024)
            .build();
    videos.add(video);

    // When
    when(videoFactory.buildVideo(any(VideoRequest.class))).thenReturn(videos);
    when(videoService.createVideo(any(), any()))
        .thenThrow(new FunctionalErrorException("Error creating videos"));

    // Then
    mockMvc
        .perform(
            post(RequestUriApi.VIDEO_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest());

    verify(videoFactory, times(1)).buildVideo(any(VideoRequest.class));
    verify(videoService, times(1)).createVideo(any(), any());
  }

  @Test
  void createVideos_whenFactoryThrowsException_shouldReturnError() throws Exception {
    // Given
    String articleId = "article123";

    VideoDto videoDto =
        VideoDto.builder()
            .name("Car Front View")
            .format("mp4")
            .path("/videos/car-front.mp4")
            .size(1024)
            .build();

    VideoRequest request =
        VideoRequest.builder()
            .articleId(articleId)
            .videos(Collections.singletonList(videoDto))
            .build();

    // When
    when(videoFactory.buildVideo(any(VideoRequest.class)))
        .thenThrow(new RuntimeException("Error building videos"));

    // Then
    mockMvc
        .perform(
            post(RequestUriApi.VIDEO_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isInternalServerError());

    verify(videoFactory, times(1)).buildVideo(any(VideoRequest.class));
  }
}
