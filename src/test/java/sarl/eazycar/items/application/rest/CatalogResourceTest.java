package sarl.eazycar.items.application.rest;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import sarl.eazycar.items.application.dto.CatalogDto;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.application.utils.RequestUriApi;
import sarl.eazycar.items.domain.entity.Catalog;
import sarl.eazycar.items.domain.service.ICatalogService;

/** Unit tests for {@link CatalogResource} using WebMvcTest */
@WebMvcTest(CatalogResource.class)
class CatalogResourceTest {

  @Autowired private MockMvc mockMvc;

  @Autowired private ObjectMapper objectMapper;

  @MockBean private ICatalogService catalogService;

  @Test
  void createCatalog_shouldReturnCreatedCatalog() throws Exception {
    // Given
    CatalogDto inputDto =
        CatalogDto.builder()
            .code("TEST-CODE")
            .service("Test Service")
            .description("Test Description")
            .build();

    Catalog savedEntity =
        Catalog.builder()
            .catalogId("123456")
            .code("TEST-CODE")
            .service("Test Service")
            .description("Test Description")
            .build();

    // When
    when(catalogService.createCatalog(any(Catalog.class))).thenReturn(savedEntity);

    // Then
    mockMvc
        .perform(
            post(RequestUriApi.CATALOG_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(inputDto)))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.catalogId", is("123456")))
        .andExpect(jsonPath("$.code", is("TEST-CODE")))
        .andExpect(jsonPath("$.service", is("Test Service")))
        .andExpect(jsonPath("$.description", is("Test Description")));

    verify(catalogService, times(1)).createCatalog(any(Catalog.class));
  }

  @Test
  void updateCatalog_shouldReturnUpdatedCatalog() throws Exception {
    // Given
    String catalogId = "123456";
    CatalogDto inputDto =
        CatalogDto.builder()
            .code("UPDATED-CODE")
            .service("Updated Service")
            .description("Updated Description")
            .build();

    Catalog updatedEntity =
        Catalog.builder()
            .catalogId(catalogId)
            .code("UPDATED-CODE")
            .service("Updated Service")
            .description("Updated Description")
            .build();

    // When
    when(catalogService.updateCatalog(eq(catalogId), any(Catalog.class))).thenReturn(updatedEntity);

    // Then
    mockMvc
        .perform(
            put(RequestUriApi.CATALOG_ENDPOINT + "/" + catalogId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(inputDto)))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.catalogId", is(catalogId)))
        .andExpect(jsonPath("$.code", is("UPDATED-CODE")))
        .andExpect(jsonPath("$.service", is("Updated Service")))
        .andExpect(jsonPath("$.description", is("Updated Description")));

    verify(catalogService, times(1)).updateCatalog(eq(catalogId), any(Catalog.class));
  }

  @Test
  void getCatalogById_shouldReturnCatalog() throws Exception {
    // Given
    String catalogId = "123456";
    Catalog foundEntity =
        Catalog.builder()
            .catalogId(catalogId)
            .code("TEST-CODE")
            .service("Test Service")
            .description("Test Description")
            .build();

    // When
    when(catalogService.getCatalogById(catalogId)).thenReturn(foundEntity);

    // Then
    mockMvc
        .perform(
            get(RequestUriApi.CATALOG_ENDPOINT + "/" + catalogId)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.catalogId", is(catalogId)))
        .andExpect(jsonPath("$.code", is("TEST-CODE")))
        .andExpect(jsonPath("$.service", is("Test Service")))
        .andExpect(jsonPath("$.description", is("Test Description")));

    verify(catalogService, times(1)).getCatalogById(catalogId);
  }

  @Test
  void getAllCatalogs_shouldReturnAllCatalogs() throws Exception {
    // Given
    Catalog catalog1 =
        Catalog.builder()
            .catalogId("123456")
            .code("CODE-1")
            .service("Service 1")
            .description("Description 1")
            .build();

    Catalog catalog2 =
        Catalog.builder()
            .catalogId("789012")
            .code("CODE-2")
            .service("Service 2")
            .description("Description 2")
            .build();

    List<Catalog> catalogs = Arrays.asList(catalog1, catalog2);

    // When
    when(catalogService.getAllCatalogs()).thenReturn(catalogs);

    // Then
    mockMvc
        .perform(get(RequestUriApi.CATALOG_ENDPOINT).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$", hasSize(2)))
        .andExpect(jsonPath("$[0].catalogId", is("123456")))
        .andExpect(jsonPath("$[0].code", is("CODE-1")))
        .andExpect(jsonPath("$[0].service", is("Service 1")))
        .andExpect(jsonPath("$[0].description", is("Description 1")))
        .andExpect(jsonPath("$[1].catalogId", is("789012")))
        .andExpect(jsonPath("$[1].code", is("CODE-2")))
        .andExpect(jsonPath("$[1].service", is("Service 2")))
        .andExpect(jsonPath("$[1].description", is("Description 2")));

    verify(catalogService, times(1)).getAllCatalogs();
  }

  @Test
  void createCatalog_whenServiceThrowsException_shouldReturnError() throws Exception {
    // Given
    CatalogDto inputDto =
        CatalogDto.builder()
            .code("DUPLICATE-CODE")
            .service("Test Service")
            .description("Test Description")
            .build();

    // When
    when(catalogService.createCatalog(any(Catalog.class)))
        .thenThrow(new FunctionalErrorException("Catalog with this code already exists"));

    // Then
    mockMvc
        .perform(
            post(RequestUriApi.CATALOG_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(inputDto)))
        .andExpect(status().isBadRequest());

    verify(catalogService, times(1)).createCatalog(any(Catalog.class));
  }

  @Test
  void updateCatalog_whenServiceThrowsException_shouldReturnError() throws Exception {
    // Given
    String catalogId = "non-existent-id";
    CatalogDto inputDto =
        CatalogDto.builder()
            .code("UPDATED-CODE")
            .service("Updated Service")
            .description("Updated Description")
            .build();

    // When
    when(catalogService.updateCatalog(eq(catalogId), any(Catalog.class)))
        .thenThrow(new FunctionalErrorException("Catalog not found"));

    // Then
    mockMvc
        .perform(
            put(RequestUriApi.CATALOG_ENDPOINT + "/" + catalogId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(inputDto)))
        .andExpect(status().isBadRequest());

    verify(catalogService, times(1)).updateCatalog(eq(catalogId), any(Catalog.class));
  }

  @Test
  void getCatalogById_whenServiceThrowsException_shouldReturnError() throws Exception {
    // Given
    String catalogId = "non-existent-id";

    // When
    when(catalogService.getCatalogById(catalogId))
        .thenThrow(new FunctionalErrorException("Catalog not found"));

    // Then
    mockMvc
        .perform(
            get(RequestUriApi.CATALOG_ENDPOINT + "/" + catalogId)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());

    verify(catalogService, times(1)).getCatalogById(catalogId);
  }

  @Test
  void getAllCatalogs_whenServiceThrowsException_shouldReturnError() throws Exception {
    // Given
    RuntimeException exception = new RuntimeException("Database connection error");

    // When
    when(catalogService.getAllCatalogs()).thenThrow(exception);

    // Then
    mockMvc
        .perform(get(RequestUriApi.CATALOG_ENDPOINT).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isInternalServerError());

    verify(catalogService, times(1)).getAllCatalogs();
  }
}
