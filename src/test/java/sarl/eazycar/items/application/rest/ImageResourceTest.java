package sarl.eazycar.items.application.rest;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import sarl.eazycar.items.application.dto.ImageDto;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.application.rest.request.ImageRequest;
import sarl.eazycar.items.application.utils.RequestUriApi;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Image;
import sarl.eazycar.items.domain.service.impl.ImageService;
import sarl.eazycar.items.infrastructure.factory.ImageFactory;

/** Unit tests for {@link ImageResource} using WebMvcTest */
@WebMvcTest(ImageResource.class)
class ImageResourceTest {

  @Autowired private MockMvc mockMvc;

  @Autowired private ObjectMapper objectMapper;

  @MockBean private ImageService imageService;

  @MockBean private ImageFactory imageFactory;

  @Test
  void createImages_shouldReturnCreatedImages() throws Exception {
    // Given
    String articleId = "article123";

    // Create image DTOs for the request
    ImageDto imageDto1 =
        ImageDto.builder()
            .name("car-front.jpg")
            .path("/images/car-front.jpg")
            .format("jpg")
            .size(1024)
            .build();

    ImageDto imageDto2 =
        ImageDto.builder()
            .name("car-interior.png")
            .path("/images/car-interior.png")
            .format("png")
            .size(2048)
            .build();

    List<ImageDto> imageDtos = Arrays.asList(imageDto1, imageDto2);

    // Create the request object
    ImageRequest request = ImageRequest.builder().articleId(articleId).images(imageDtos).build();

    // Create Article entity
    Article article = Article.builder().articleId(articleId).build();

    // Create Image entities that will be returned by the service
    Image image1 =
        Image.builder()
            .fileId("file123")
            .name("car-front.jpg")
            .path("/images/car-front.jpg")
            .format("jpg")
            .size(1024)
            .article(article)
            .build();

    Image image2 =
        Image.builder()
            .fileId("file456")
            .name("car-interior.png")
            .path("/images/car-interior.png")
            .format("png")
            .size(2048)
            .article(article)
            .build();

    List<Image> images = Arrays.asList(image1, image2);

    // When
    when(imageFactory.buildImage(any(ImageRequest.class))).thenReturn(images);
    when(imageService.createImage(any(), any())).thenReturn(images);

    // Then
    mockMvc
        .perform(
            post(RequestUriApi.IMAGE_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$", hasSize(2)))
        .andExpect(jsonPath("$[0].fileId", is("file123")))
        .andExpect(jsonPath("$[0].name", is("car-front.jpg")))
        .andExpect(jsonPath("$[0].path", is("/images/car-front.jpg")))
        .andExpect(jsonPath("$[0].format", is("jpg")))
        .andExpect(jsonPath("$[0].size", is(1024)))
        .andExpect(jsonPath("$[1].fileId", is("file456")))
        .andExpect(jsonPath("$[1].name", is("car-interior.png")))
        .andExpect(jsonPath("$[1].path", is("/images/car-interior.png")))
        .andExpect(jsonPath("$[1].format", is("png")))
        .andExpect(jsonPath("$[1].size", is(2048)));

    verify(imageFactory, times(1)).buildImage(any(ImageRequest.class));
    verify(imageService, times(1)).createImage(any(), any());
  }

  @Test
  void createImages_withEmptyImagesList_shouldReturnEmptyList() throws Exception {
    // Given
    String articleId = "article123";

    // Create the request object with empty images list
    ImageRequest request =
        ImageRequest.builder().articleId(articleId).images(new ArrayList<>()).build();

    // When
    when(imageFactory.buildImage(any(ImageRequest.class))).thenReturn(new ArrayList<>());
    when(imageService.createImage(any(), any())).thenReturn(new ArrayList<>());

    // Then
    mockMvc
        .perform(
            post(RequestUriApi.IMAGE_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$", hasSize(0)));

    verify(imageFactory, times(1)).buildImage(any(ImageRequest.class));
    verify(imageService, times(1)).createImage(any(), any());
  }

  @Test
  void createImages_withNonExistentArticleId_shouldReturnError() throws Exception {
    // Given
    String nonExistentArticleId = "nonExistentArticle";

    ImageDto imageDto =
        ImageDto.builder()
            .name("car-front.jpg")
            .path("/images/car-front.jpg")
            .format("jpg")
            .size(1024)
            .build();

    List<ImageDto> imageDtos = List.of(imageDto);

    // Create the request object
    ImageRequest request =
        ImageRequest.builder().articleId(nonExistentArticleId).images(imageDtos).build();

    // When
    when(imageFactory.buildImage(any(ImageRequest.class)))
        .thenThrow(
            new FunctionalErrorException("Article with id " + nonExistentArticleId + " not found"));

    // Then
    mockMvc
        .perform(
            post(RequestUriApi.IMAGE_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isBadRequest());

    verify(imageFactory, times(1)).buildImage(any(ImageRequest.class));
  }

  @Test
  void createImages_whenServiceThrowsException_shouldReturnError() throws Exception {
    // Given
    String articleId = "article123";

    ImageDto imageDto =
        ImageDto.builder()
            .name("car-front.jpg")
            .path("/images/car-front.jpg")
            .format("jpg")
            .size(1024)
            .build();

    List<ImageDto> imageDtos = List.of(imageDto);

    // Create the request object
    ImageRequest request = ImageRequest.builder().articleId(articleId).images(imageDtos).build();

    List<Image> images = new ArrayList<>();

    // When
    when(imageFactory.buildImage(any(ImageRequest.class))).thenReturn(images);
    when(imageService.createImage(any(), any()))
        .thenThrow(new RuntimeException("Database error while saving images"));

    // Then
    mockMvc
        .perform(
            post(RequestUriApi.IMAGE_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isInternalServerError());

    verify(imageFactory, times(1)).buildImage(any(ImageRequest.class));
    verify(imageService, times(1)).createImage(any(), any());
  }
}
