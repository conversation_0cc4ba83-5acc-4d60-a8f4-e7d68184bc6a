package sarl.eazycar.items.application.rest;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import sarl.eazycar.items.application.dto.ContractDto;
import sarl.eazycar.items.application.dto.InvoiceDto;
import sarl.eazycar.items.application.dto.OrderDto;
import sarl.eazycar.items.application.dto.PaymentDto;
import sarl.eazycar.items.application.rest.request.PaymentRequest;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Contract;
import sarl.eazycar.items.domain.entity.Invoice;
import sarl.eazycar.items.domain.entity.Payment;
import sarl.eazycar.items.domain.entity.Purchase;
import sarl.eazycar.items.domain.entity.enums.ContractStatus;
import sarl.eazycar.items.domain.entity.enums.InvoiceStatus;
import sarl.eazycar.items.domain.entity.enums.OrderStatus;
import sarl.eazycar.items.domain.entity.enums.PaymentMethod;
import sarl.eazycar.items.domain.entity.enums.PaymentStatus;
import sarl.eazycar.items.domain.service.IContractService;
import sarl.eazycar.items.domain.service.IInvoiceService;
import sarl.eazycar.items.domain.service.IOrderService;
import sarl.eazycar.items.domain.service.IPaymentService;
import sarl.eazycar.items.infrastructure.mapper.ContractMapper;
import sarl.eazycar.items.infrastructure.mapper.InvoiceMapper;
import sarl.eazycar.items.infrastructure.mapper.OrderMapper;
import sarl.eazycar.items.infrastructure.mapper.PaymentMapper;

/** Tests d'intégration pour OrderResource */
@WebMvcTest(OrderResource.class)
class OrderResourceTest {

  @Autowired private MockMvc mockMvc;

  @Autowired private ObjectMapper objectMapper;

  @MockBean private IOrderService orderService;

  @MockBean private IInvoiceService invoiceService;

  @MockBean private IPaymentService paymentService;

  @MockBean private IContractService contractService;

  @MockBean private OrderMapper orderMapper;

  @MockBean private InvoiceMapper invoiceMapper;

  @MockBean private PaymentMapper paymentMapper;

  @MockBean private ContractMapper contractMapper;

  private Purchase testOrder;
  private OrderDto testOrderDto;
  private Invoice testInvoice;
  private InvoiceDto testInvoiceDto;
  private Payment testPayment;
  private PaymentDto testPaymentDto;
  private Contract testContract;
  private ContractDto testContractDto;
  private Article testArticle;

  @BeforeEach
  void setUp() {
    // Créer un article de test
    testArticle =
        Article.builder().articleId("article-123").name("Test Car").available(true).build();

    // Créer une commande de test
    testOrder = new Purchase();
    testOrder.setOrderId("order-123");
    testOrder.setQuantity(1);
    testOrder.setUnitPrice(BigDecimal.valueOf(100.00));
    testOrder.setArticle(testArticle);
    testOrder.setOrderDate(LocalDateTime.now());
    testOrder.setStatus(OrderStatus.CONFIRMED);
    testOrder.setAccountId("account-123");

    // Créer un DTO de commande de test
    testOrderDto =
        OrderDto.builder()
            .orderId("order-123")
            .quantity(1)
            .unitPrice(BigDecimal.valueOf(100.00))
            .status(OrderStatus.CONFIRMED)
            .accountId("account-123")
            .orderType("PURCHASE")
            .build();

    // Créer une facture de test
    testInvoice =
        Invoice.builder()
            .invoiceId("invoice-123")
            .issueDate(LocalDate.now())
            .dueDate(LocalDate.now().plusDays(30))
            .status(InvoiceStatus.SENT)
            .order(testOrder)
            .build();

    // Créer un DTO de facture de test
    testInvoiceDto =
        InvoiceDto.builder()
            .invoiceId("invoice-123")
            .issueDate(LocalDate.now())
            .dueDate(LocalDate.now().plusDays(30))
            .status(InvoiceStatus.SENT)
            .order(testOrderDto)
            .build();

    // Créer un paiement de test
    testPayment =
        Payment.builder()
            .paymentId("payment-123")
            .paymentDate(LocalDateTime.now())
            .amount(BigDecimal.valueOf(100.00))
            .paymentMethod(PaymentMethod.CREDIT_CARD)
            .transactionId("txn-123456")
            .status(PaymentStatus.SUCCESSFUL)
            .invoice(testInvoice)
            .build();

    // Créer un DTO de paiement de test
    testPaymentDto =
        PaymentDto.builder()
            .paymentId("payment-123")
            .amount(BigDecimal.valueOf(100.00))
            .paymentMethod(PaymentMethod.CREDIT_CARD)
            .transactionId("txn-123456")
            .status(PaymentStatus.SUCCESSFUL)
            .invoice(testInvoiceDto)
            .build();

    // Créer un contrat de test
    testContract =
        Contract.builder()
            .contractId("contract-123")
            .documentUrl("https://example.com/contract.pdf")
            .status(ContractStatus.PENDING_SIGNATURE)
            .order(testOrder)
            .build();

    // Créer un DTO de contrat de test
    testContractDto =
        ContractDto.builder()
            .contractId("contract-123")
            .documentUrl("https://example.com/contract.pdf")
            .status(ContractStatus.PENDING_SIGNATURE)
            .build();
  }

  @Test
  @DisplayName("Should get orders by account ID successfully")
  void shouldGetOrdersByAccountIdSuccessfully() throws Exception {
    // Given
    when(orderService.getOrdersByAccountId("account-123")).thenReturn(List.of(testOrder));
    when(orderMapper.toDto(testOrder)).thenReturn(testOrderDto);

    // When & Then
    mockMvc
        .perform(get("/api/v1/orders/account/account-123"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$").isArray())
        .andExpect(jsonPath("$[0].orderId").value("order-123"))
        .andExpect(jsonPath("$[0].status").value("CONFIRMED"))
        .andExpect(jsonPath("$[0].accountId").value("account-123"));
  }

  @Test
  @DisplayName("Should get order by ID successfully")
  void shouldGetOrderByIdSuccessfully() throws Exception {
    // Given
    when(orderService.getOrderById("order-123")).thenReturn(testOrder);
    when(orderMapper.toDto(testOrder)).thenReturn(testOrderDto);

    // When & Then
    mockMvc
        .perform(get("/api/v1/orders/order-123"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.orderId").value("order-123"))
        .andExpect(jsonPath("$.status").value("CONFIRMED"))
        .andExpect(jsonPath("$.unitPrice").value(100.00));
  }

  @Test
  @DisplayName("Should update order status successfully")
  void shouldUpdateOrderStatusSuccessfully() throws Exception {
    // Given
    testOrder.setStatus(OrderStatus.CONFIRMED);
    testOrderDto.setStatus(OrderStatus.CONFIRMED);

    when(orderService.updateOrderStatus("order-123", OrderStatus.CONFIRMED)).thenReturn(testOrder);
    when(orderMapper.toDto(testOrder)).thenReturn(testOrderDto);

    // When & Then
    mockMvc
        .perform(put("/api/v1/orders/order-123/status").param("status", "CONFIRMED"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.orderId").value("order-123"))
        .andExpect(jsonPath("$.status").value("CONFIRMED"));
  }

  @Test
  @DisplayName("Should get invoice by order ID successfully")
  void shouldGetInvoiceByOrderIdSuccessfully() throws Exception {
    // Given
    when(invoiceService.getInvoiceByOrderId("order-123")).thenReturn(testInvoice);
    when(invoiceMapper.toDto(testInvoice)).thenReturn(testInvoiceDto);

    // When & Then
    mockMvc
        .perform(get("/api/v1/orders/order-123/invoice"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.invoiceId").value("invoice-123"))
        .andExpect(jsonPath("$.status").value("SENT"))
        .andExpect(jsonPath("$.order.orderId").value("order-123"));
  }

  @Test
  @DisplayName("Should get payments by order ID successfully")
  void shouldGetPaymentsByOrderIdSuccessfully() throws Exception {
    // Given
    when(invoiceService.getInvoiceByOrderId("order-123")).thenReturn(testInvoice);
    when(paymentService.getPaymentsByInvoiceId("invoice-123")).thenReturn(List.of(testPayment));
    when(paymentMapper.toDto(testPayment)).thenReturn(testPaymentDto);

    // When & Then
    mockMvc
        .perform(get("/api/v1/orders/order-123/invoice/payments"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$").isArray())
        .andExpect(jsonPath("$[0].paymentId").value("payment-123"))
        .andExpect(jsonPath("$[0].amount").value(100.00))
        .andExpect(jsonPath("$[0].status").value("SUCCESSFUL"));
  }

  @Test
  @DisplayName("Should record payment successfully")
  void shouldRecordPaymentSuccessfully() throws Exception {
    // Given
    PaymentRequest paymentRequest =
        PaymentRequest.builder()
            .amount(BigDecimal.valueOf(100.00))
            .paymentMethod(PaymentMethod.CREDIT_CARD)
            .transactionId("txn-123456")
            .build();

    when(invoiceService.getInvoiceByOrderId("order-123")).thenReturn(testInvoice);
    when(paymentService.recordPayment(
            "invoice-123", BigDecimal.valueOf(100.00), PaymentMethod.CREDIT_CARD, "txn-123456"))
        .thenReturn(testPayment);
    when(paymentMapper.toDto(testPayment)).thenReturn(testPaymentDto);

    // When & Then
    mockMvc
        .perform(
            post("/api/v1/orders/order-123/invoice/payments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(paymentRequest)))
        .andExpect(status().isCreated())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.paymentId").value("payment-123"))
        .andExpect(jsonPath("$.amount").value(100.00))
        .andExpect(jsonPath("$.transactionId").value("txn-123456"));
  }

  @Test
  @DisplayName("Should get contract by order ID successfully")
  void shouldGetContractByOrderIdSuccessfully() throws Exception {
    // Given
    when(contractService.getContractByOrderId("order-123")).thenReturn(testContract);
    when(contractMapper.toDto(testContract)).thenReturn(testContractDto);

    // When & Then
    mockMvc
        .perform(get("/api/v1/orders/order-123/contract"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.contractId").value("contract-123"))
        .andExpect(jsonPath("$.status").value("PENDING_SIGNATURE"))
        .andExpect(jsonPath("$.documentUrl").value("https://example.com/contract.pdf"));
  }

  @Test
  @DisplayName("Should sign contract successfully")
  void shouldSignContractSuccessfully() throws Exception {
    // Given
    Contract signedContract =
        Contract.builder()
            .contractId("contract-123")
            .documentUrl("https://example.com/contract.pdf")
            .status(ContractStatus.SIGNED)
            .signatureDate(LocalDateTime.now())
            .order(testOrder)
            .build();

    ContractDto signedContractDto =
        ContractDto.builder()
            .contractId("contract-123")
            .documentUrl("https://example.com/contract.pdf")
            .status(ContractStatus.SIGNED)
            .signatureDate(LocalDateTime.now())
            .build();

    when(contractService.getContractByOrderId("order-123")).thenReturn(testContract);
    when(contractService.signContract(any(), any())).thenReturn(signedContract);
    when(contractMapper.toDto(signedContract)).thenReturn(signedContractDto);

    // When & Then
    mockMvc
        .perform(put("/api/v1/orders/order-123/contract/sign"))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.contractId").value("contract-123"))
        .andExpect(jsonPath("$.status").value("SIGNED"));
  }

  @Test
  @DisplayName("Should return not found for invalid order ID")
  void shouldReturnNotFoundForInvalidOrderId() throws Exception {
    // Given
    when(orderService.getOrderById("invalid-order"))
        .thenThrow(new RuntimeException("Order not found"));

    // When & Then
    mockMvc
        .perform(get("/api/v1/orders/invalid-order"))
        .andExpect(status().isInternalServerError());
  }
}
