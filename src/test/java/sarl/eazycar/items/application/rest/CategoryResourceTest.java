package sarl.eazycar.items.application.rest;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import sarl.eazycar.items.application.dto.CategoryDto;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.application.utils.RequestUriApi;
import sarl.eazycar.items.domain.entity.Category;
import sarl.eazycar.items.domain.service.ICategoryService;

/** Tests for {@link CategoryResource} */
@WebMvcTest(CategoryResource.class)
class CategoryResourceTest {

  @Autowired private MockMvc mockMvc;

  @MockBean private ICategoryService categoryService;

  @Autowired private ObjectMapper objectMapper;

  private CategoryDto categoryDto;

  private Category savedCategory;

  @BeforeEach
  void setUp() {
    // Setup test data
    categoryDto =
        CategoryDto.builder()
            .type("SUV")
            .label("Sport Utility Vehicle")
            .description(
                "A type of car that combines elements of road-going passenger cars with features from off-road vehicles")
            .minPrice(new BigDecimal("20000"))
            .maxPrice(new BigDecimal("50000"))
            .build();

    savedCategory =
        Category.builder()
            .categoryId("123456")
            .type("SUV")
            .label("Sport Utility Vehicle")
            .description(
                "A type of car that combines elements of road-going passenger cars with features from off-road vehicles")
            .minPrice(new BigDecimal("20000"))
            .maxPrice(new BigDecimal("50000"))
            .build();
  }

  @Test
  void createCategory_shouldReturnCreatedCategory() throws Exception {
    // Given
    when(categoryService.createCategory(any(Category.class))).thenReturn(savedCategory);

    // When & Then
    mockMvc
        .perform(
            post(RequestUriApi.CATEGORY_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(categoryDto)))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.categoryId").value("123456"))
        .andExpect(jsonPath("$.type").value("SUV"))
        .andExpect(jsonPath("$.label").value("Sport Utility Vehicle"))
        .andExpect(
            jsonPath("$.description")
                .value(
                    "A type of car that combines elements of road-going passenger cars with features from off-road vehicles"))
        .andExpect(jsonPath("$.minPrice").value(20000))
        .andExpect(jsonPath("$.maxPrice").value(50000));
  }

  @Test
  void updateCategory_shouldReturnUpdatedCategory() throws Exception {
    // Given
    String categoryId = "123456";
    CategoryDto updatedCategoryDto =
        CategoryDto.builder()
            .type("SUV")
            .label("Luxury SUV")
            .description("A luxury sport utility vehicle with premium features")
            .minPrice(new BigDecimal("30000"))
            .maxPrice(new BigDecimal("80000"))
            .build();

    Category resultCategory =
        Category.builder()
            .categoryId(categoryId)
            .type("SUV")
            .label("Luxury SUV")
            .description("A luxury sport utility vehicle with premium features")
            .minPrice(new BigDecimal("30000"))
            .maxPrice(new BigDecimal("80000"))
            .build();

    when(categoryService.updateCategory(anyString(), any(Category.class)))
        .thenReturn(resultCategory);

    // When & Then
    mockMvc
        .perform(
            put(RequestUriApi.CATEGORY_ENDPOINT + "/{categoryId}", categoryId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updatedCategoryDto)))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.categoryId").value(categoryId))
        .andExpect(jsonPath("$.type").value("SUV"))
        .andExpect(jsonPath("$.label").value("Luxury SUV"))
        .andExpect(
            jsonPath("$.description").value("A luxury sport utility vehicle with premium features"))
        .andExpect(jsonPath("$.minPrice").value(30000))
        .andExpect(jsonPath("$.maxPrice").value(80000));
  }

  @Test
  void getCategoryById_shouldReturnCategory() throws Exception {
    // Given
    String categoryId = "123456";
    when(categoryService.getCategoryById(categoryId)).thenReturn(savedCategory);

    // When & Then
    mockMvc
        .perform(
            get(RequestUriApi.CATEGORY_ENDPOINT + "/{categoryId}", categoryId)
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$.categoryId").value(categoryId))
        .andExpect(jsonPath("$.type").value("SUV"))
        .andExpect(jsonPath("$.label").value("Sport Utility Vehicle"))
        .andExpect(
            jsonPath("$.description")
                .value(
                    "A type of car that combines elements of road-going passenger cars with features from off-road vehicles"))
        .andExpect(jsonPath("$.minPrice").value(20000))
        .andExpect(jsonPath("$.maxPrice").value(50000));
  }

  @Test
  void getAllCategories_shouldReturnAllCategories() throws Exception {
    // Given
    Category category2 =
        Category.builder()
            .categoryId("789012")
            .type("SEDAN")
            .label("Sedan")
            .description("A passenger car with a three-box configuration")
            .minPrice(new BigDecimal("15000"))
            .maxPrice(new BigDecimal("40000"))
            .build();

    List<Category> categories = Arrays.asList(savedCategory, category2);
    when(categoryService.getAllCategories()).thenReturn(categories);

    // When & Then
    mockMvc
        .perform(get(RequestUriApi.CATEGORY_ENDPOINT).contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(jsonPath("$[0].categoryId").value("123456"))
        .andExpect(jsonPath("$[0].type").value("SUV"))
        .andExpect(jsonPath("$[1].categoryId").value("789012"))
        .andExpect(jsonPath("$[1].type").value("SEDAN"))
        .andExpect(jsonPath("$[1].label").value("Sedan"));
  }

  @Test
  void createCategory_withInvalidData_shouldHandleException() throws Exception {
    // Given
    when(categoryService.createCategory(any(Category.class)))
        .thenThrow(new FunctionalErrorException("The min price must be less than the max price."));

    // When & Then
    mockMvc
        .perform(
            post(RequestUriApi.CATEGORY_ENDPOINT)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(categoryDto)))
        .andExpect(status().isBadRequest());
  }

  @Test
  void updateCategory_nonExistentCategory_shouldHandleException() throws Exception {
    // Given
    String nonExistentCategoryId = "nonExistentId";
    when(categoryService.updateCategory(anyString(), any(Category.class)))
        .thenThrow(new FunctionalErrorException("Category with id nonExistentId not found"));

    // When & Then
    mockMvc
        .perform(
            put(RequestUriApi.CATEGORY_ENDPOINT + "/{categoryId}", nonExistentCategoryId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(categoryDto)))
        .andExpect(status().isBadRequest());
  }

  @Test
  void getCategoryById_nonExistentCategory_shouldHandleException() throws Exception {
    // Given
    String nonExistentCategoryId = "nonExistentId";
    when(categoryService.getCategoryById(nonExistentCategoryId))
        .thenThrow(new FunctionalErrorException("Category with id nonExistentId not found"));

    // When & Then
    mockMvc
        .perform(
            get(RequestUriApi.CATEGORY_ENDPOINT + "/{categoryId}", nonExistentCategoryId)
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }
}
