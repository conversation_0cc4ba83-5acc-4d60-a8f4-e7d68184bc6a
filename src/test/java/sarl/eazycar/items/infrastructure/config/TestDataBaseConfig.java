package sarl.eazycar.items.infrastructure.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

import java.util.Optional;

/**
 * Test configuration for database auditing
 */
@TestConfiguration
@EnableJpaAuditing(auditorAwareRef = "springAuditorAware")
public class TestDataBaseConfig {
    @Bean
    public AuditorAware<String> springAuditorAware() {
        return () -> Optional.of("testUser");
    }
}