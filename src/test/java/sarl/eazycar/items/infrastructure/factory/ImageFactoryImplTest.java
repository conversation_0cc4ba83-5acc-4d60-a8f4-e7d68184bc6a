package sarl.eazycar.items.infrastructure.factory;

import static org.junit.jupiter.api.Assertions.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import sarl.eazycar.items.application.dto.ImageDto;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.application.rest.request.ImageRequest;
import sarl.eazycar.items.domain.entity.Image;

class ImageFactoryImplTest {

  private ImageFactoryImpl imageFactory;

  @BeforeEach
  void setUp() {
    imageFactory = new ImageFactoryImpl();
  }

  @Test
  void buildImage_withValidRequest_shouldReturnImagesList() {
    // Given
    String articleId = "article123";

    ImageDto imageDto1 =
        ImageDto.builder()
            .name("car-front.jpg")
            .path("/images/car-front.jpg")
            .format("jpg")
            .size(1024)
            .build();

    ImageDto imageDto2 =
        ImageDto.builder()
            .name("car-interior.png")
            .path("/images/car-interior.png")
            .format("png")
            .size(2048)
            .build();

    List<ImageDto> imageDtos = Arrays.asList(imageDto1, imageDto2);

    ImageRequest request = ImageRequest.builder().articleId(articleId).images(imageDtos).build();

    // When
    List<Image> result = imageFactory.buildImage(request);

    // Then
    assertEquals(2, result.size());

    // Verify first image
    Image firstImage = result.get(0);
    assertEquals("car-front.jpg", firstImage.getName());
    assertEquals("/images/car-front.jpg", firstImage.getPath());
    assertEquals("jpg", firstImage.getFormat());
    assertEquals(1024, firstImage.getSize());

    // Verify second image
    Image secondImage = result.get(1);
    assertEquals("car-interior.png", secondImage.getName());
    assertEquals("/images/car-interior.png", secondImage.getPath());
    assertEquals("png", secondImage.getFormat());
    assertEquals(2048, secondImage.getSize());
  }

  @Test
  void buildImage_withNullImages_shouldThrowsFunctionalErrorException() {
    // Given
    String articleId = "article123";
    ImageRequest request = ImageRequest.builder().articleId(articleId).images(null).build();

    // When
    FunctionalErrorException exception =
        assertThrows(FunctionalErrorException.class, () -> imageFactory.buildImage(request));
    // Then
    assertEquals("Images must be provided", exception.getMessage());
  }

  @Test
  void buildImage_withEmptyImagesList_shouldThrowsFunctionalErrorException() {
    // Given
    String articleId = "article123";
    ImageRequest request =
        ImageRequest.builder().articleId(articleId).images(new ArrayList<>()).build();

    // When
    FunctionalErrorException exception =
        assertThrows(FunctionalErrorException.class, () -> imageFactory.buildImage(request));
    // Then
    assertEquals("Images must be provided", exception.getMessage());
  }

  @Test
  void buildImage_shouldMapAllImageProperties() {
    // Given
    String articleId = "article123";

    ImageDto imageDto =
        ImageDto.builder()
            .name("test-image.jpg")
            .path("/images/test-image.jpg")
            .format("jpg")
            .size(512)
            .build();

    List<ImageDto> imageDtos = List.of(imageDto);

    ImageRequest request = ImageRequest.builder().articleId(articleId).images(imageDtos).build();

    // When
    List<Image> result = imageFactory.buildImage(request);

    // Then
    assertEquals(1, result.size());

    Image image = result.get(0);
    assertEquals("test-image.jpg", image.getName());
    assertEquals("/images/test-image.jpg", image.getPath());
    assertEquals("jpg", image.getFormat());
    assertEquals(512, image.getSize());
  }

  @Test
  void buildImage_withNullArticleId_shouldThrowsFunctionalErrorException() {
    // Given
    ImageDto imageDto = ImageDto.builder().name("name").build();

    ImageRequest imageRequest =
        ImageRequest.builder().articleId(null).images(Collections.singletonList(imageDto)).build();

    // When
    FunctionalErrorException exception =
        assertThrows(FunctionalErrorException.class, () -> imageFactory.buildImage(imageRequest));

    // Then
    assertEquals("Article id must be provided", exception.getMessage());
  }
}
