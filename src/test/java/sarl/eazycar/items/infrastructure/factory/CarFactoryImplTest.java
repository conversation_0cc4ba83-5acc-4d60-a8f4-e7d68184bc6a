package sarl.eazycar.items.infrastructure.factory;

import static org.junit.jupiter.api.Assertions.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import sarl.eazycar.items.application.rest.request.CarRequest;
import sarl.eazycar.items.application.rest.request.OptionRequest;
import sarl.eazycar.items.domain.entity.Article;
import sarl.eazycar.items.domain.entity.Car;
import sarl.eazycar.items.domain.entity.Price;

class CarFactoryImplTest {

  private CarFactoryImpl carFactoryImpl;

  @BeforeEach
  void setUp() {
    carFactoryImpl = new CarFactoryImpl();
  }

  @Test
  void buildCar_withValidData_shouldCreateCarWithCorrectProperties() {
    // Arrange
    String categoryId = "category123";
    String brand = "Toyota";
    String model = "Camry";
    Integer factoryYear = 2023;
    String name = "Toyota Camry 2023";
    String description = "A reliable sedan";
    String cityLocation = "New York";
    String districtLocation = "Manhattan";
    boolean available = true;

    OptionRequest carOption =
        OptionRequest.builder()
            .priceId("price123")
            .articleOptionId("option123")
            .catalogId("catalog123")
            .price(BigDecimal.valueOf(25000))
            .currency("USD")
            .discountPercent(BigDecimal.valueOf(5))
            .build();

    List<OptionRequest> carOptions = Collections.singletonList(carOption);

    CarRequest carRequest =
        CarRequest.builder()
            .categoryId(categoryId)
            .brand(brand)
            .model(model)
            .factoryYear(factoryYear)
            .name(name)
            .description(description)
            .cityLocation(cityLocation)
            .districtLocation(districtLocation)
            .available(available)
            .options(carOptions)
            .build();

    // Act
    Car car = carFactoryImpl.buildCar(carRequest);

    // Assert
    assertNotNull(car);
    assertEquals(brand, car.getBrand());
    assertEquals(model, car.getModel());
    assertEquals(factoryYear, car.getFactoryYear());

    Article article = car.getArticle();
    assertNotNull(article);
    assertEquals(name, article.getName());
    assertEquals(description, article.getDescription());
    assertEquals(cityLocation, article.getCityLocation());
    assertEquals(districtLocation, article.getDistrictLocation());
    assertEquals(available, article.isAvailable());
    assertEquals(categoryId, article.getCategory().getCategoryId());

    List<Price> prices = article.getPrices();
    assertEquals(1, prices.size());

    Price price = prices.get(0);
    assertEquals(carOption.getPriceId(), price.getPriceId());
    assertEquals(carOption.getPrice(), price.getPrice());
    assertEquals(carOption.getCurrency(), price.getCurrency());
    assertEquals(carOption.getDiscountPercent(), price.getDiscountPercent());
    assertEquals(carOption.getArticleOptionId(), price.getArticleOption().getOptionId());
    assertEquals(carOption.getCatalogId(), price.getCatalog().getCatalogId());
  }

  @Test
  void buildCar_withMultipleOptions_shouldCreateCarWithMultiplePrices() {
    // Arrange
    OptionRequest option1 =
        OptionRequest.builder()
            .priceId("price1")
            .articleOptionId("option1")
            .catalogId("catalog1")
            .price(BigDecimal.valueOf(25000))
            .currency("USD")
            .discountPercent(BigDecimal.valueOf(5))
            .build();

    OptionRequest option2 =
        OptionRequest.builder()
            .priceId("price2")
            .articleOptionId("option2")
            .catalogId("catalog2")
            .price(BigDecimal.valueOf(2000))
            .currency("USD")
            .discountPercent(BigDecimal.valueOf(0))
            .build();

    OptionRequest option3 =
        OptionRequest.builder()
            .priceId("price3")
            .articleOptionId("option3")
            .catalogId("catalog3")
            .price(BigDecimal.valueOf(1500))
            .currency("USD")
            .discountPercent(BigDecimal.valueOf(10))
            .build();

    List<OptionRequest> carOptions = List.of(option1, option2, option3);

    CarRequest carRequest =
        CarRequest.builder()
            .categoryId("category123")
            .brand("Honda")
            .model("Accord")
            .factoryYear(2024)
            .name("Honda Accord 2024")
            .description("Luxury sedan")
            .cityLocation("Los Angeles")
            .districtLocation("Beverly Hills")
            .available(true)
            .options(carOptions)
            .build();

    // Act
    Car car = carFactoryImpl.buildCar(carRequest);

    // Assert
    assertNotNull(car);
    Article article = car.getArticle();
    assertNotNull(article);

    List<Price> prices = article.getPrices();
    assertEquals(3, prices.size());

    // Verify each price has the correct properties and references
    for (int i = 0; i < prices.size(); i++) {
      Price price = prices.get(i);
      OptionRequest option = carOptions.get(i);

      assertEquals(option.getPriceId(), price.getPriceId());
      assertEquals(option.getPrice(), price.getPrice());
      assertEquals(option.getCurrency(), price.getCurrency());
      assertEquals(option.getDiscountPercent(), price.getDiscountPercent());
      assertEquals(option.getArticleOptionId(), price.getArticleOption().getOptionId());
      assertEquals(option.getCatalogId(), price.getCatalog().getCatalogId());

      // Verify bidirectional relationship
      assertSame(article, price.getArticle());
    }
  }

  @Test
  void buildCar_withNoOptions_shouldCreateCarWithEmptyPricesList() {
    // Arrange
    CarRequest carRequest =
        CarRequest.builder()
            .categoryId("category123")
            .brand("Ford")
            .model("Mustang")
            .factoryYear(2022)
            .name("Ford Mustang 2022")
            .description("Sports car")
            .cityLocation("Detroit")
            .districtLocation("Downtown")
            .available(true)
            .options(Collections.emptyList())
            .build();

    // Act
    Car car = carFactoryImpl.buildCar(carRequest);

    // Assert
    assertNotNull(car);
    Article article = car.getArticle();
    assertNotNull(article);

    List<Price> prices = article.getPrices();
    assertNotNull(prices);
    assertTrue(prices.isEmpty());
  }

  @Test
  void buildCar_shouldSetupPriceArticleRelationship() {
    // Arrange
    OptionRequest option =
        OptionRequest.builder()
            .priceId("price123")
            .articleOptionId("option123")
            .catalogId("catalog123")
            .price(BigDecimal.valueOf(30000))
            .currency("USD")
            .discountPercent(BigDecimal.valueOf(7))
            .build();

    CarRequest carRequest =
        CarRequest.builder()
            .categoryId("category123")
            .brand("Nissan")
            .model("Altima")
            .factoryYear(2023)
            .name("Nissan Altima 2023")
            .description("Mid-size sedan")
            .cityLocation("Chicago")
            .districtLocation("Loop")
            .available(true)
            .options(Collections.singletonList(option))
            .build();

    // Act
    Car car = carFactoryImpl.buildCar(carRequest);

    // Assert
    Article article = car.getArticle();
    List<Price> prices = article.getPrices();

    // Verify bidirectional relationship is properly set up
    for (Price price : prices) {
      assertSame(article, price.getArticle());
    }
  }

  @Test
  void buildCar_shouldMapCarPropertiesCorrectly() {
    // Arrange
    String brand = "Hyundai";
    String model = "Sonata";
    Integer factoryYear = 2024;

    CarRequest carRequest =
        CarRequest.builder()
            .categoryId("category123")
            .brand(brand)
            .model(model)
            .factoryYear(factoryYear)
            .name("Hyundai Sonata 2024")
            .description("Affordable sedan")
            .cityLocation("Miami")
            .districtLocation("South Beach")
            .available(true)
            .options(new ArrayList<>())
            .build();

    // Act
    Car car = carFactoryImpl.buildCar(carRequest);

    // Assert
    assertNotNull(car);
    assertEquals(brand, car.getBrand());
    assertEquals(model, car.getModel());
    assertEquals(factoryYear, car.getFactoryYear());
  }

  @Test
  void buildCar_shouldMapArticlePropertiesCorrectly() {
    // Arrange
    String name = "Kia Sportage 2023";
    String description = "Compact SUV";
    String cityLocation = "Seattle";
    String districtLocation = "Downtown";
    boolean available = true;
    String categoryId = "category456";

    CarRequest carRequest =
        CarRequest.builder()
            .categoryId(categoryId)
            .brand("Kia")
            .model("Sportage")
            .factoryYear(2023)
            .name(name)
            .description(description)
            .cityLocation(cityLocation)
            .districtLocation(districtLocation)
            .available(available)
            .options(new ArrayList<>())
            .build();

    // Act
    Car car = carFactoryImpl.buildCar(carRequest);

    // Assert
    Article article = car.getArticle();
    assertNotNull(article);
    assertEquals(name, article.getName());
    assertEquals(description, article.getDescription());
    assertEquals(cityLocation, article.getCityLocation());
    assertEquals(districtLocation, article.getDistrictLocation());
    assertEquals(available, article.isAvailable());
    assertEquals(categoryId, article.getCategory().getCategoryId());
  }

  @Test
  void buildCar_shouldMapPricePropertiesCorrectly() {
    // Arrange
    String priceId = "price789";
    BigDecimal priceValue = BigDecimal.valueOf(45000);
    String currency = "EUR";
    BigDecimal discountPercent = BigDecimal.valueOf(15);
    String articleOptionId = "option789";
    String catalogId = "catalog789";

    OptionRequest option =
        OptionRequest.builder()
            .priceId(priceId)
            .articleOptionId(articleOptionId)
            .catalogId(catalogId)
            .price(priceValue)
            .currency(currency)
            .discountPercent(discountPercent)
            .build();

    CarRequest carRequest =
        CarRequest.builder()
            .categoryId("category123")
            .brand("BMW")
            .model("X5")
            .factoryYear(2023)
            .name("BMW X5 2023")
            .description("Luxury SUV")
            .cityLocation("Berlin")
            .districtLocation("Mitte")
            .available(true)
            .options(Collections.singletonList(option))
            .build();

    // Act
    Car car = carFactoryImpl.buildCar(carRequest);

    // Assert
    Article article = car.getArticle();
    List<Price> prices = article.getPrices();
    assertEquals(1, prices.size());

    Price price = prices.get(0);
    assertEquals(priceId, price.getPriceId());
    assertEquals(priceValue, price.getPrice());
    assertEquals(currency, price.getCurrency());
    assertEquals(discountPercent, price.getDiscountPercent());
    assertEquals(articleOptionId, price.getArticleOption().getOptionId());
    assertEquals(catalogId, price.getCatalog().getCatalogId());
  }

  @Test
  void buildCar_withNullCarOptions_shouldHandleGracefully() {
    // Arrange
    CarRequest carRequest =
        CarRequest.builder()
            .categoryId("category123")
            .brand("Audi")
            .model("A4")
            .factoryYear(2023)
            .name("Audi A4 2023")
            .description("Luxury sedan")
            .cityLocation("Munich")
            .districtLocation("Schwabing")
            .available(true)
            .options(null)
            .build();

    // Act
    Car car = carFactoryImpl.buildCar(carRequest);

    // Assert
    assertNotNull(car);
    Article article = car.getArticle();
    assertNotNull(article);

    // The implementation should handle null carOptions gracefully
    // Either by creating an empty list or handling the null case
    assertNotNull(article.getPrices());
  }
}
