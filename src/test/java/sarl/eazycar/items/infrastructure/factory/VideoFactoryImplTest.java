package sarl.eazycar.items.infrastructure.factory;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import sarl.eazycar.items.application.dto.VideoDto;
import sarl.eazycar.items.application.exception.FunctionalErrorException;
import sarl.eazycar.items.application.rest.request.VideoRequest;
import sarl.eazycar.items.domain.entity.Video;

class VideoFactoryImplTest {

  private VideoFactoryImpl videoFactoryImpl;

  @BeforeEach
  void setUp() {
    videoFactoryImpl = new VideoFactoryImpl();
  }

  @Test
  void buildVideo_withValidData_shouldCreateVideoWithCorrectProperties() {
    // Given
    String articleId = "article123";
    String name = "Sample Video";
    String path = "/videos/sample.mp4";
    String format = "mp4";
    int size = 1024;

    VideoDto videoDto = VideoDto.builder().name(name).path(path).format(format).size(size).build();

    VideoRequest videoRequest =
        VideoRequest.builder()
            .articleId(articleId)
            .videos(Collections.singletonList(videoDto))
            .build();

    // When
    List<Video> videos = videoFactoryImpl.buildVideo(videoRequest);

    // Then
    assertNotNull(videos);
    assertEquals(1, videos.size());

    Video video = videos.get(0);
    assertEquals(name, video.getName());
    assertEquals(path, video.getPath());
    assertEquals(format, video.getFormat());
    assertEquals(size, video.getSize());
  }

  @Test
  void buildVideo_withMultipleEntries_shouldCreateMultipleVideos() {
    // Given
    String articleId = "article456";

    VideoDto videoDto1 =
        VideoDto.builder()
            .name("Video 1")
            .path("/videos/video1.mp4")
            .format("mp4")
            .size(1024)
            .build();

    VideoDto videoDto2 =
        VideoDto.builder()
            .name("Video 2")
            .path("/videos/video2.avi")
            .format("avi")
            .size(2048)
            .build();

    VideoDto videoDto3 =
        VideoDto.builder()
            .name("Video 3")
            .path("/videos/video3.mov")
            .format("mov")
            .size(3072)
            .build();

    List<VideoDto> videoDtos = Arrays.asList(videoDto1, videoDto2, videoDto3);

    VideoRequest videoRequest =
        VideoRequest.builder().articleId(articleId).videos(videoDtos).build();

    // When
    List<Video> videos = videoFactoryImpl.buildVideo(videoRequest);

    // Then
    assertNotNull(videos);
    assertEquals(3, videos.size());

    // Verify each video has the correct properties
    for (int i = 0; i < videos.size(); i++) {
      Video video = videos.get(i);
      VideoDto dto = videoDtos.get(i);

      assertEquals(dto.getName(), video.getName());
      assertEquals(dto.getPath(), video.getPath());
      assertEquals(dto.getFormat(), video.getFormat());
      assertEquals(dto.getSize(), video.getSize());
    }
  }

  @Test
  void buildVideo_withEmptyList_shouldThrowsFunctionalErrorException() {
    // Given
    String articleId = "article789";

    VideoRequest videoRequest =
        VideoRequest.builder().articleId(articleId).videos(Collections.emptyList()).build();

    // When
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> videoFactoryImpl.buildVideo(videoRequest));

    // Then
    assertEquals("Videos must be provided", exception.getMessage());
  }

  @Test
  void buildVideo_withNullList_shouldThrowsFunctionalErrorException() {
    // Given
    String articleId = "article789";

    VideoRequest videoRequest = VideoRequest.builder().articleId(articleId).videos(null).build();

    // When
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> videoFactoryImpl.buildVideo(videoRequest));

    // Then
    assertEquals("Videos must be provided", exception.getMessage());
  }

  @Test
  void buildVideo_withNullArticleId_shouldThrowsFunctionalErrorException() {
    // Given
    VideoDto videoDto = VideoDto.builder().name("name").build();

    VideoRequest videoRequest =
        VideoRequest.builder().articleId(null).videos(Collections.singletonList(videoDto)).build();

    // When
    FunctionalErrorException exception =
        assertThrows(
            FunctionalErrorException.class, () -> videoFactoryImpl.buildVideo(videoRequest));

    // Then
    assertEquals("Article id must be provided", exception.getMessage());
  }
}
