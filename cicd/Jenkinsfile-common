#!/usr/bin/env groovy
println('load file common')

def mavenTask(String command) {
    println('mavenTask execution command')
    def MAVEN_BUILD = tool 'maven_3.9.8'
    def JAVA_HOME = tool 'jdk_17'
    if(isUnix()) {
        withEnv(["JAVA_HOME=${JAVA_HOME}"]) {
            println('execute command line with sh')
            sh "'${MAVEN_BUILD}/bin/mvn' ${command}"
        }
    } else {
        println('execute command line with bat')
    }
}

def nodeTask(String command) {
    println('nodeTask execution command')
    def NODE_HOME = tool 'nodejs_22.3.0'
    if(isUnix()) {
        withEnv(["PATH=${NODE_HOME};${env.PATH}"]) {
            println('execute command line with sh')
            sh "${command}"
        }
    } else {
        println('execute command line with bat')
    }
}

def envTask(String branchName) {
    println('envTask execution with '+branchName)
    if( branchName != null && branchName == 'main'){
        return 'production'
    } else if( branchName != null && branchName.startsWith('release')){
        return 'release'
    } else {
        return 'develop'
    }
}

def artifactTask(String branchName, String currentVersion, String command) {
    println('artifactTask execution with '+branchName+' and command : '+ command)
    if( branchName != null && branchName == 'main'){
        if(currentVersion != null && currentVersion.contains('SNAPSHOT')) {
            def newVersion = currentVersion.replace('-SNAPSHOT', '')
            mavenTask("-s settings.xml versions:set -DnewVersion=${newVersion}")
            mavenTask("-s settings.xml versions:commit")
        }
        mavenTask(command)
    } else {
        mavenTask("-s settings.xml deploy -DskipTests")
    }
}

def slackNotify(String result){
    println('slackNotify execution command with status '+result)
    if(result == "SUCCESS") {
        slackSend color: 'good', message: "COMMUNICATION: Job ${env.JOB_NAME} with buildNumber ${env.BUILD_NUMBER} was successful ! \n more info ${env.BUILD_URL}"
    } else if(result == "FAILURE") {
        slackSend color: 'danger', message: "BAD NEWS: Job ${env.JOB_NAME} with buildNumber ${env.BUILD_NUMBER} was failed ! \n more info ${env.BUILD_URL}"
    } else if(result == "UNSTABLE") {
        slackSend color: 'warning', message: "BAD NEWS: Job ${env.JOB_NAME} with buildNumber ${env.BUILD_NUMBER} was unstable ! \n  more info ${env.BUILD_URL}"
    } else {
        slackSend color: 'danger', message: "BAD NEWS: Job ${env.JOB_NAME} with buildNumber ${env.BUILD_NUMBER} its result was unclear ! \n more info ${env.BUILD_URL}"
    }
}

return this
