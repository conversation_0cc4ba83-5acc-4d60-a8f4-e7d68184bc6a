version: "3.8"

services:
  items-app:
    image: app-image
    labels:
      com.docker.stack.namespace: "devops"
    environment:
      - JAVA_OPTS=-Xms1G -Xmx2G -XX:+UseG1GC
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=postgres
      - SPRING_DATASOURCE_URL=****************************************
    networks:
      - infra_network
    deploy:
      mode: replicated
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first
        failure_action: rollback
      rollback_config:
        parallelism: 0
        delay: 10s
        order: stop-first
      placement:
        constraints: [ node.role == worker ]

networks:
  infra_network:
    name: infra_network
    external: true