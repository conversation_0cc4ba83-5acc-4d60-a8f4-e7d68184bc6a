#!/usr/bin/env groovy

pipeline {
    agent any

    environment {
        JAVA_TOOLS = 'jdk_17'
        MAVEN_TOOLS = 'maven_3.9.8'

        PROJECT_VERSION = readMavenPom().getVersion()
        PROJECT_ARTIFACT_ID = readMavenPom().getArtifactId()
        PROJECT_GROUP_ID = readMavenPom().getGroupId()

        common = ''
        envImage = ''
        packagejar = ''
    }

    tools {
        maven "${MAVEN_TOOLS}"
        jdk "${JAVA_TOOLS}"
        dockerTool 'docker'
    }

    options {
        buildDiscarder(logRotator(numToKeepStr: '10'))
        disableConcurrentBuilds()
        timestamps()
    }

    stages {
        stage('Git Source Checkout') {
            steps {
                checkout scm
            }
        }

        stage('Build Maven') {
            steps {
                withMaven(jdk: "${JAVA_TOOLS}", maven: "${MAVEN_TOOLS}") {
                    sh 'mvn -s settings.xml clean install -DskipTests'
                }
            }
        }

        stage('Build Test') {
            steps {
                script {
                    common = load('./cicd/Jenkinsfile-common')
                    common.mavenTask("-s settings.xml install test")
                }
            }
        }

        stage('Trivy Scan Repo') {
            steps {
                script {
                    def result = sh(script: 'docker ps -a --format "{{.Names}}" | grep -q "trivy"', returnStatus: true)
                    if (result == 0) {
                        echo "Le conteneur trivy existe, suppression..."
                        sh 'docker container rm trivy --force'
                        sh 'docker run --name trivy -v /var/jenkins_home:/root/.cache/ aquasec/trivy:0.52.2 fs . > trivy.txt'
                        sh 'docker container rm trivy --force'
                    } else {
                        echo "Le conteneur trivy n'existe pas."
                        sh 'docker run --name trivy -v /var/jenkins_home:/root/.cache/ aquasec/trivy:0.52.2 fs . > trivy.txt'
                        sh 'docker container rm trivy --force'
                    }
                }
            }
        }

        stage('Sonar Analysis') {
            environment {
                sonar_token = credentials('sonarqube-token')
            }
            steps {
                withMaven(jdk: "${JAVA_TOOLS}", maven: "${MAVEN_TOOLS}") {
                    withSonarQubeEnv('sonarqube_server') {
                        sh label: "Static Code Analysis", script: 'mvn -B -s settings.xml -DskipTests verify sonar:sonar -Dsonar.token=$sonar_token -Dsonar.projectName=${PROJECT_ARTIFACT_ID}/${BRANCH_NAME} -Dsonar.projectKey=${PROJECT_ARTIFACT_ID} -Dsonar.projectVersion=${PROJECT_VERSION}'
                    }
                }
            }
        }

        stage("Quality Gate") {
            steps {
                script {
                    def qualityGate = waitForQualityGate abortPipeline: false, credentialsId: 'sonarqube-token'
                    if (qualityGate.status != 'OK') {
                        currentBuild.result = 'FAILURE'
                        error "Quality Gate failed: ${qualityGate.status}"
                    }
                }
            }
        }

        stage('Publish Artifact on Nexus') {
            when {
                anyOf {
                    branch pattern: "release/.*", comparator: "REGEXP"
                    branch 'main'
                }
            }
            steps {
                script {
                    common.artifactTask("${BRANCH_NAME}", "${PROJECT_VERSION}", "-s settings.xml deploy -Pprod -DskipTests")
                }
            }
        }

        stage('Build Image') {
            when {
                anyOf {
                    branch pattern: "release/.*", comparator: "REGEXP"
                    branch 'main'
                }
            }
            steps {
                script {
                    envImage = common.envTask("${BRANCH_NAME}")
                    packagejar = "target/${PROJECT_ARTIFACT_ID}-${PROJECT_VERSION}.jar"
                    withDockerRegistry(credentialsId: 'registry-auth', toolName: 'docker', url: 'https://registry.eazycar.sarl') {
                        sh label: 'Build Image', script: "docker build -t registry.eazycar.sarl/${envImage}/${PROJECT_ARTIFACT_ID}:${PROJECT_VERSION} -f cicd/Dockerfile --build-arg JAR_FILE=${packagejar} ."
                    }
                }
            }
        }

        stage('Publish Image on Nexus') {
            when {
                anyOf {
                    branch pattern: "release/.*", comparator: "REGEXP"
                    branch 'main'
                }
            }
            steps {
                script {
                    packagejar = "target/${PROJECT_ARTIFACT_ID}-${PROJECT_VERSION}.jar"
                    envImage = common.envTask("${BRANCH_NAME}")
                    withDockerRegistry(credentialsId: 'registry-auth', toolName: 'docker', url: 'https://registry.eazycar.sarl') {
                        sh "echo 'Nexus Registry Docker Connect'"
                        sh label: 'Tag Image to latest', script: "docker tag registry.eazycar.sarl/${envImage}/${PROJECT_ARTIFACT_ID}:${PROJECT_VERSION} registry.eazycar.sarl/${envImage}/${PROJECT_ARTIFACT_ID}:latest"
                        sh label: 'Push Image to Registry', script: "docker push registry.eazycar.sarl/${envImage}/${PROJECT_ARTIFACT_ID}:${PROJECT_VERSION}"
                        sh label: 'Push Image latest to Registry', script: "docker push registry.eazycar.sarl/${envImage}/${PROJECT_ARTIFACT_ID}:latest"
                        sh label: 'Remove Old Image', script: "docker image rm registry.eazycar.sarl/${envImage}/${PROJECT_ARTIFACT_ID}:${PROJECT_VERSION}"
                        sh label: 'Remove latest Image', script: "docker image rm registry.eazycar.sarl/${envImage}/${PROJECT_ARTIFACT_ID}:latest"
                    }
                }
            }
        }

        stage('Trigger job deployment') {
            when {
                anyOf {
                    branch pattern: "release/.*", comparator: "REGEXP"
                    branch 'main'
                }
            }
            steps {
                script {
                    build wait: false,
                            job: 'eazycar-cd/items-app-cd',
                            parameters: [
                                    string(name: 'branchName', value: "${BRANCH_NAME}"),
                                    string(name: 'imageTag', value: 'latest')
                            ]
                }
            }
        }

        stage('Send Notification') {
            steps {
                script {
                    emailext attachLog: true,
                            subject: "'${currentBuild.result}'",
                            body: "Project: ${env.JOB_NAME}<br/>" +
                                    "Build Number: ${env.BUILD_NUMBER}<br/>" +
                                    "URL: ${env.BUILD_URL}<br/>",
                            to: '<EMAIL>',
                            attachmentsPattern: 'trivy.txt'
                }
            }
        }
    }

    post {
        always {
            script {
                common.slackNotify("${currentBuild.result}")
            }
            cleanWs()
        }
    }
}
