# 📋 Documentation du Design - Order Service

## 🎯 Vue d'ensemble

L'Order Service est un système de gestion de commandes, factures et paiements conçu selon les principes de l'architecture hexagonale (Clean Architecture). Il gère le cycle de vie complet des commandes depuis l'ajout au panier jusqu'au paiement final.

## 🏗️ Architecture Générale

### Principes Architecturaux
- **Architecture Hexagonale** : Séparation claire entre domaine métier et infrastructure
- **Domain-Driven Design (DDD)** : Modélisation centrée sur le métier
- **CQRS Pattern** : Séparation des opérations de lecture et d'écriture
- **Repository Pattern** : Abstraction de la couche de persistance
- **Factory Pattern** : Création d'objets complexes (Orders)

### Structure des Couches

```
┌─────────────────────────────────────────┐
│           API Layer (REST)              │
│  - CartResource, OrderResource          │
│  - DTOs, Mappers                        │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│        Application Layer                │
│  - Services d'application               │
│  - Orchestration des cas d'usage        │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│          Domain Layer                   │
│  - Entités métier                       │
│  - Services domaine                     │
│  - Interfaces repositories              │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│       Infrastructure Layer              │
│  - Repositories JPA                     │
│  - Configuration                        │
└─────────────────────────────────────────┘
```

## 🎭 Modèle de Domaine

### Entités Principales

#### 1. Order (Entité Abstraite)
```java
@Entity
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "ORDER_TYPE")
public abstract class Order extends AuditEntity {
    private String orderId;
    private LocalDateTime orderDate;
    private OrderStatus status;
    private String currency;
    private LocalDateTime confirmationDate;
    private Integer quantity;
    private BigDecimal unitPrice;
    private Article article;
    private Cart cart;
    private String accountId;
}
```

**Spécialisations :**
- **Purchase** : Commande d'achat avec adresse de livraison
- **Rental** : Commande de location avec dates et lieux

#### 2. Cart (Panier)
```java
@Entity
public class Cart extends AuditEntity {
    private String cartId;
    private CartStatus status;
    private BigDecimal totalAmount;
    private String accountId;
    private List<Order> orders;
}
```

#### 3. Invoice (Facture)
```java
@Entity
public class Invoice extends AuditEntity {
    private String invoiceId;
    private LocalDate issueDate;
    private LocalDate dueDate;
    private InvoiceStatus status;
    private Order order;
    private List<Payment> payments;
}
```

#### 4. Payment (Paiement)
```java
@Entity
public class Payment extends AuditEntity {
    private String paymentId;
    private LocalDateTime paymentDate;
    private BigDecimal amount;
    private PaymentMethod paymentMethod;
    private String transactionId;
    private PaymentStatus status;
    private Invoice invoice;
}
```

#### 5. Contract (Contrat)
```java
@Entity
public class Contract extends AuditEntity {
    private String contractId;
    private String documentUrl;
    private LocalDateTime signatureDate;
    private ContractStatus status;
    private Order order;
}
```

### Énumérations Métier

#### OrderStatus
```java
public enum OrderStatus {
    PENDING,     // En attente
    CONFIRMED,   // Confirmée
    CANCELLED,   // Annulée
    IN_CART      // Dans le panier
}
```

#### CartStatus
```java
public enum CartStatus {
    ACTIVE,      // Actif
    CHECKED_OUT  // Finalisé
}
```

#### InvoiceStatus
```java
public enum InvoiceStatus {
    DRAFT,    // Brouillon
    SENT,     // Envoyée
    PAID,     // Payée
    OVERDUE   // En retard
}
```

#### PaymentStatus
```java
public enum PaymentStatus {
    PENDING,     // En attente
    SUCCESSFUL,  // Réussi
    FAILED       // Échoué
}
```

#### PaymentMethod
```java
public enum PaymentMethod {
    CREDIT_CARD,     // Carte de crédit
    BANK_TRANSFER,   // Virement bancaire
    CASH,            // Espèces
    ORANGE_TRANSFER, // Orange Money
    MTN_TRANSFER     // MTN Mobile Money
}
```

## 🔄 Flux de Processus Métier

### 1. Processus de Commande Complet

```mermaid
graph TD
    A[Créer/Obtenir Panier] --> B[Ajouter Articles]
    B --> C[Valider Panier]
    C --> D[Processus Checkout]
    D --> E[Générer Facture]
    E --> F[Générer Contrat]
    F --> G[Enregistrer Paiement]
    G --> H[Finaliser Commande]
```

### 2. États et Transitions

#### Cycle de vie d'une Order
```
IN_CART → PENDING → CONFIRMED
    ↓         ↓
CANCELLED ← CANCELLED
```

#### Cycle de vie d'une Invoice
```
DRAFT → SENT → PAID
         ↓
      OVERDUE → PAID
```

#### Cycle de vie d'un Payment
```
PENDING → SUCCESSFUL
    ↓
  FAILED
```

## 🛠️ Services Métier

### 1. ICartService
**Responsabilités :**
- Gestion du cycle de vie des paniers
- Ajout/suppression d'articles
- Calcul des totaux

**Méthodes principales :**
```java
Cart getOrCreateActiveCart(String accountId);
Cart addOrderToCart(String accountId, Order order);
Cart removeOrderFromCart(String cartId, String orderId);
Cart calculateCartTotal(String cartId);
Cart clearCart(String accountId);
```

### 2. ICheckoutService
**Responsabilités :**
- Orchestration du processus de checkout
- Validation des paniers
- Transition des états

**Méthodes principales :**
```java
List<Invoice> processCheckout(String accountId);
void validateCartForCheckout(Cart cart);
```

**Processus de Checkout :**
1. Récupération du panier actif
2. Validation du contenu
3. Confirmation des commandes
4. Génération des factures
5. Génération des contrats
6. Finalisation du panier

### 3. IOrderService
**Responsabilités :**
- Gestion des commandes
- Mise à jour des statuts
- Requêtes sur les commandes

**Méthodes principales :**
```java
Order createOrderFromCart(String cartId, String accountId);
List<Order> getOrdersByAccountId(String accountId);
Order updateOrderStatus(String orderId, OrderStatus newStatus);
```

### 4. IInvoiceService
**Responsabilités :**
- Génération des factures
- Gestion des statuts de facture
- Calculs financiers

**Méthodes principales :**
```java
Invoice generateInvoiceForOrder(Order order);
Invoice updateInvoiceStatus(String invoiceId, InvoiceStatus newStatus);
List<Invoice> getInvoicesByAccountId(String accountId);
```

### 5. IPaymentService
**Responsabilités :**
- Enregistrement des paiements
- Validation des transactions
- Mise à jour automatique des factures

**Méthodes principales :**
```java
Payment recordPayment(String invoiceId, BigDecimal amount, 
                     PaymentMethod method, String transactionId);
List<Payment> getPaymentsByInvoiceId(String invoiceId);
Payment updatePaymentStatus(String paymentId, PaymentStatus newStatus);
```

### 6. IContractService
**Responsabilités :**
- Génération des contrats
- Gestion des signatures
- Stockage des documents

**Méthodes principales :**
```java
Contract generateContractForOrder(Order order);
Contract signContract(String contractId, LocalDateTime signatureDate);
Contract getContractByOrderId(String orderId);
```

## 🌐 API REST

### Cart API (`/api/v1/carts`)

| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/{accountId}` | Obtenir/créer panier actif |
| POST | `/{accountId}/orders` | Ajouter article au panier |
| DELETE | `/{accountId}/orders/{orderId}` | Supprimer article |
| DELETE | `/{accountId}/clear` | Vider le panier |
| POST | `/{accountId}/checkout` | Processus de checkout |

### Order API (`/api/v1/orders`)

| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/account/{accountId}` | Commandes d'un utilisateur |
| GET | `/{orderId}` | Détails d'une commande |
| PUT | `/{orderId}/status` | Mettre à jour statut |
| GET | `/{orderId}/invoice` | Facture d'une commande |
| GET | `/{orderId}/invoice/payments` | Paiements d'une facture |
| POST | `/{orderId}/invoice/payments` | Enregistrer paiement |
| GET | `/{orderId}/contract` | Contrat d'une commande |
| PUT | `/{orderId}/contract/sign` | Signer contrat |

## 📊 Modèle de Données

### Relations entre Entités

```mermaid
erDiagram
    CART ||--o{ ORDER : contains
    ORDER ||--|| INVOICE : generates
    ORDER ||--|| CONTRACT : generates
    INVOICE ||--o{ PAYMENT : receives
    ORDER }o--|| ARTICLE : references
    
    CART {
        string cart_id PK
        string account_id
        enum status
        decimal total_amount
    }
    
    ORDER {
        string order_id PK
        string cart_id FK
        string article_id FK
        string account_id
        enum status
        enum order_type
        decimal unit_price
        int quantity
    }
    
    INVOICE {
        string invoice_id PK
        string order_id FK
        date issue_date
        date due_date
        enum status
    }
    
    PAYMENT {
        string payment_id PK
        string invoice_id FK
        decimal amount
        enum payment_method
        enum status
        string transaction_id
    }
    
    CONTRACT {
        string contract_id PK
        string order_id FK
        string document_url
        datetime signature_date
        enum status
    }
```

## 🔒 Règles Métier

### Validation des Paniers
- Un panier ne peut être finalisé que s'il contient au moins une commande
- Toutes les commandes doivent avoir un statut `IN_CART`
- Les articles doivent être disponibles

### Gestion des Factures
- Une facture ne peut être générée qu'une seule fois par commande
- Les transitions de statut suivent un ordre strict : `DRAFT → SENT → PAID`
- Une facture `OVERDUE` peut devenir `PAID`

### Gestion des Paiements
- Un `transactionId` doit être unique dans le système
- Le montant doit être positif
- Les paiements partiels sont autorisés
- Le statut de la facture est mis à jour automatiquement

### Gestion des Contrats
- Un contrat est généré automatiquement lors du checkout
- La signature met à jour le statut à `SIGNED`
- L'URL du document doit être valide

## 🧪 Stratégie de Test

### Tests Unitaires
- **CartServiceTest** : Tests complets du service panier
- **CheckoutServiceTest** : Tests du processus de checkout
- **InvoiceServiceTest** : Tests du service facture
- **PaymentServiceTest** : Tests du service paiement
- **ContractServiceTest** : Tests du service contrat

### Tests d'Intégration
- **CartResourceTest** : Tests des API REST du panier
- **OrderResourceTest** : Tests des API REST des commandes

### Scénarios de Test
1. **Cycle complet** : Panier → Checkout → Paiement → Signature
2. **Gestion d'erreurs** : Validation des données, états invalides
3. **Concurrence** : Accès simultané aux paniers
4. **Performance** : Calculs de totaux, requêtes complexes

## 🚀 Exemple d'Utilisation

### Scénario Complet via API

```bash
# 1. Créer/obtenir un panier
GET /api/v1/carts/user-123

# 2. Ajouter un article
POST /api/v1/carts/user-123/orders
{
  "articleId": "car-456",
  "quantity": 1,
  "unitPrice": 25000,
  "orderType": "PURCHASE",
  "shippingAddress": "123 Main St"
}

# 3. Processus de checkout
POST /api/v1/carts/user-123/checkout

# 4. Enregistrer un paiement
POST /api/v1/orders/order-123/invoice/payments
{
  "amount": 25000,
  "paymentMethod": "CREDIT_CARD",
  "transactionId": "txn-789"
}

# 5. Signer le contrat
PUT /api/v1/orders/order-123/contract/sign
{
  "signatureDate": "2024-01-15T10:30:00"
}
```

## 🔧 Configuration et Déploiement

### Propriétés de Configuration
```yaml
order-service:
  invoice:
    default-due-days: 30
  payment:
    supported-methods: 
      - CREDIT_CARD
      - BANK_TRANSFER
      - ORANGE_TRANSFER
      - MTN_TRANSFER
  contract:
    document-storage-path: "/contracts"
```

### Base de Données
- **Stratégie d'héritage** : `SINGLE_TABLE` pour Order/Purchase/Rental
- **Identifiants** : TSID pour performance et unicité
- **Audit** : Traçabilité automatique via `AuditEntity`

## 📈 Métriques et Monitoring

### KPIs Métier
- Taux de conversion panier → commande
- Temps moyen de checkout
- Taux de paiement réussi
- Délai moyen de signature des contrats

### Métriques Techniques
- Temps de réponse des API
- Taux d'erreur par endpoint
- Utilisation des ressources
- Performance des requêtes

## 🔮 Évolutions Futures

### Fonctionnalités Prévues
- **Paiements en plusieurs fois** : Support des échéanciers
- **Remboursements** : Gestion des annulations et remboursements
- **Notifications** : Alertes automatiques (factures, paiements)
- **Workflow avancé** : Approbations multi-niveaux
- **Intégrations** : Systèmes de paiement externes

### Améliorations Techniques
- **Event Sourcing** : Traçabilité complète des événements
- **SAGA Pattern** : Gestion des transactions distribuées
- **Cache distribué** : Amélioration des performances
- **API GraphQL** : Requêtes flexibles côté client

---

*Cette documentation présente le design actuel de l'Order Service. Elle sera mise à jour au fur et à mesure des évolutions du système.*
